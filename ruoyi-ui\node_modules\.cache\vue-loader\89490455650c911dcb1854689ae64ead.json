{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue?vue&type=template&id=eba18020&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}