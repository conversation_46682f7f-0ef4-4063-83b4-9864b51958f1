{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\api\\biz\\questionBank.js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\api\\biz\\questionBank.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listQuestionBank", "query", "request", "url", "method", "params", "getQuestionBank", "bankId", "addQuestionBank", "data", "updateQuestionBank", "delQuestionBank", "debugDocumentContent", "file", "formData", "FormData", "append", "headers", "batchImportQuestions", "uploadAndParse", "saveEditedContent", "previewContent", "importFromEditor"], "sources": ["D:/IDEA_PROJECT/exam/ruoyi-ui/src/api/biz/questionBank.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询题库列表\r\nexport function listQuestionBank(query) {\r\n  return request({\r\n    url: '/biz/questionBank/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询题库详细\r\nexport function getQuestionBank(bankId) {\r\n  return request({\r\n    url: '/biz/questionBank/' + bankId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增题库\r\nexport function addQuestionBank(data) {\r\n  return request({\r\n    url: '/biz/questionBank',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改题库\r\nexport function updateQuestionBank(data) {\r\n  return request({\r\n    url: '/biz/questionBank',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除题库\r\nexport function delQuestionBank(bankId) {\r\n  return request({\r\n    url: '/biz/questionBank/' + bankId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 调试文档内容\r\nexport function debugDocumentContent(file) {\r\n  const formData = new FormData()\r\n  formData.append('file', file)\r\n  return request({\r\n    url: '/biz/questionBank/debugDocumentContent',\r\n    method: 'post',\r\n    data: formData,\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data'\r\n    }\r\n  })\r\n}\r\n\r\n// 批量导入题目\r\nexport function batchImportQuestions(data) {\r\n  return request({\r\n    url: '/biz/questionBank/batchImportQuestions',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 上传文档并解析 - 新版本支持富文本编辑\r\nexport function uploadAndParse(file, bankId) {\r\n  const formData = new FormData()\r\n  formData.append('file', file)\r\n  formData.append('bankId', bankId)\r\n  return request({\r\n    url: '/biz/questionBank/uploadAndParse',\r\n    method: 'post',\r\n    data: formData,\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data'\r\n    }\r\n  })\r\n}\r\n\r\n// 保存编辑后的内容\r\nexport function saveEditedContent(data) {\r\n  return request({\r\n    url: '/biz/questionBank/saveEditedContent',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 实时预览编辑内容\r\nexport function previewContent(data) {\r\n  return request({\r\n    url: '/biz/questionBank/previewContent',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 从编辑器内容导入题目\r\nexport function importFromEditor(data) {\r\n  return request({\r\n    url: '/biz/questionBank/importFromEditor',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACC,MAAM,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,kBAAkBA,CAACD,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,eAAeA,CAACJ,MAAM,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,oBAAoBA,CAACC,IAAI,EAAE;EACzC,IAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;EAC7B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEK,QAAQ;IACdG,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,oBAAoBA,CAACT,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,cAAcA,CAACN,IAAI,EAAEN,MAAM,EAAE;EAC3C,IAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;EAC7BC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAET,MAAM,CAAC;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEK,QAAQ;IACdG,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,iBAAiBA,CAACX,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,cAAcA,CAACZ,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,gBAAgBA,CAACb,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}