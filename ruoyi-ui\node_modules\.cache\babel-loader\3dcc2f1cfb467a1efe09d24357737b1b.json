{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "_questionBank", "name", "props", "visible", "type", "Boolean", "default", "bankId", "String", "Number", "required", "bankName", "data", "dialogVisible", "currentStep", "uploadedFile", "uploading", "uploadProgress", "<PERSON><PERSON><PERSON><PERSON>", "previewHtml", "previewQuestionCount", "previewLoading", "parseErrors", "importing", "importOptions", "allowDuplicate", "importResult", "successCount", "failCount", "errors", "watch", "val", "resetImport", "$emit", "methods", "customUpload", "option", "_this", "file", "uploadAndParse", "then", "response", "code", "edit<PERSON><PERSON><PERSON><PERSON>", "$message", "success", "onSuccess", "error", "msg", "onError", "Error", "catch", "console", "beforeFileUpload", "isDocx", "toLowerCase", "endsWith", "isLt10M", "size", "handleFileSuccess", "handleFileError", "err", "onContentChange", "_this2", "clearTimeout", "previewTimer", "setTimeout", "autoPreview", "trim", "previewContent", "saveContent", "_this3", "content", "saveEditedContent", "_this4", "questionCount", "confirmImport", "_this5", "importFromEditor", "nextStep", "prevStep", "handleComplete", "handleClose", "loadDemoContent", "demoContent"], "sources": ["src/views/biz/questionBank/components/RichTextImport.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    title=\"题库文档导入\"\n    :visible.sync=\"dialogVisible\"\n    width=\"95%\"\n    :before-close=\"handleClose\"\n    append-to-body\n    class=\"rich-text-import-dialog\"\n  >\n    <div class=\"import-container\">\n      <!-- 步骤指示器 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"上传文档\"></el-step>\n        <el-step title=\"编辑内容\"></el-step>\n        <el-step title=\"预览确认\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 文档上传 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <h3>上传Word文档</h3>\n          <p>请上传包含题目的Word文档(.docx格式)，系统将自动解析文档内容</p>\n\n          <div class=\"demo-section\">\n            <el-button size=\"small\" type=\"text\" @click=\"loadDemoContent\">\n              <i class=\"el-icon-magic-stick\"></i> 加载演示内容\n            </el-button>\n            <span class=\"demo-tip\">或者点击加载演示内容快速体验功能</span>\n          </div>\n\n          <el-upload\n            ref=\"fileUpload\"\n            :http-request=\"customUpload\"\n            :on-success=\"handleFileSuccess\"\n            :on-error=\"handleFileError\"\n            :before-upload=\"beforeFileUpload\"\n            :show-file-list=\"false\"\n            :accept=\".docx\"\n            drag\n          >\n            <div class=\"upload-area\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"upload-text\">\n                <p>将Word文档拖到此处，或<em>点击上传</em></p>\n                <p class=\"upload-tip\">支持 .docx 格式文件，文件大小不超过10MB</p>\n              </div>\n            </div>\n          </el-upload>\n\n          <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n            <el-alert\n              :title=\"`已上传文件：${uploadedFile.name}`\"\n              type=\"success\"\n              :closable=\"false\"\n              show-icon\n            />\n          </div>\n\n          <div v-if=\"uploading\" class=\"uploading-status\">\n            <el-progress :percentage=\"50\" :show-text=\"false\"></el-progress>\n            <p>正在上传并解析文档...</p>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"handleClose\">取消</el-button>\n          <el-button type=\"primary\" :disabled=\"!uploadedFile\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 富文本编辑 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"editor-container\">\n          <div class=\"editor-header\">\n            <h3>编辑文档内容</h3>\n            <div class=\"editor-actions\">\n              <el-button size=\"small\" @click=\"saveContent\">保存内容</el-button>\n              <el-button size=\"small\" type=\"primary\" @click=\"previewContent\">实时预览</el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-preview-layout\">\n            <!-- 左侧富文本编辑器 -->\n            <div class=\"editor-panel\">\n              <div class=\"panel-header\">\n                <h4>编辑区域</h4>\n                <span class=\"panel-tip\">您可以在此编辑题目内容</span>\n              </div>\n              <div class=\"editor-wrapper\">\n                <el-input\n                  v-model=\"editContent\"\n                  type=\"textarea\"\n                  :rows=\"15\"\n                  placeholder=\"请编辑题目内容...\"\n                  @input=\"onContentChange\"\n                  class=\"content-editor\"\n                />\n              </div>\n            </div>\n\n            <!-- 右侧预览区域 -->\n            <div class=\"preview-panel\">\n              <div class=\"panel-header\">\n                <h4>实时预览</h4>\n                <span class=\"panel-tip\">题目解析结果：{{ previewQuestionCount }}道题目</span>\n              </div>\n              <div class=\"preview-wrapper\">\n                <div v-if=\"previewLoading\" class=\"preview-loading\">\n                  <el-loading text=\"正在解析预览...\"></el-loading>\n                </div>\n                <div v-else-if=\"previewHtml\" class=\"preview-content\" v-html=\"previewHtml\"></div>\n                <div v-else class=\"preview-empty\">\n                  <el-empty description=\"暂无预览内容，请编辑左侧内容\"></el-empty>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :disabled=\"!editContent\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 预览确认 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"confirm-section\">\n          <h3>确认导入</h3>\n          <div class=\"import-summary\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析题目数量\" :value=\"previewQuestionCount\" suffix=\"道\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析错误\" :value=\"parseErrors.length\" suffix=\"个\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"目标题库\" :value=\"bankName\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <h4>解析错误信息</h4>\n            <el-alert\n              v-for=\"(error, index) in parseErrors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"warning\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n\n          <div class=\"import-options\">\n            <h4>导入选项</h4>\n            <el-checkbox v-model=\"importOptions.allowDuplicate\">允许导入重复题目</el-checkbox>\n          </div>\n\n          <div class=\"final-preview\">\n            <h4>最终预览</h4>\n            <div class=\"preview-content\" v-html=\"previewHtml\"></div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :loading=\"importing\" @click=\"confirmImport\">确认导入</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 64px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          \n          <div class=\"import-result\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"成功导入\" :value=\"importResult.successCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"导入失败\" :value=\"importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"总计处理\" :value=\"importResult.successCount + importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"import-errors\">\n            <h4>导入错误详情</h4>\n            <el-alert\n              v-for=\"(error, index) in importResult.errors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"error\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { uploadAndParse, saveEditedContent, previewContent, importFromEditor } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"RichTextImport\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    bankName: {\n      type: String,\n      default: '题库'\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n      \n      // 文件上传相关\n      uploadedFile: null,\n      uploading: false,\n      uploadProgress: 0,\n      \n      // 编辑器相关\n      editContent: '',\n      \n      // 预览相关\n      previewHtml: '',\n      previewQuestionCount: 0,\n      previewLoading: false,\n      parseErrors: [],\n      \n      // 导入相关\n      importing: false,\n      importOptions: {\n        allowDuplicate: false\n      },\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  methods: {\n    // 自定义上传方法\n    customUpload(option) {\n      const file = option.file\n      this.uploading = true\n      this.uploadProgress = 0\n\n      uploadAndParse(file, this.bankId).then(response => {\n        this.uploading = false\n        if (response.code === 200) {\n          this.uploadedFile = {\n            name: file.name,\n            response: response\n          }\n          this.editContent = response.data.editableContent || ''\n          this.parseErrors = response.data.errors || []\n          this.$message.success('文档上传成功')\n          option.onSuccess(response, file)\n        } else {\n          this.$message.error(response.msg || '文档上传失败')\n          option.onError(new Error(response.msg || '上传失败'), file)\n        }\n      }).catch(error => {\n        this.uploading = false\n        console.error('文件上传失败', error)\n        this.$message.error('文件上传失败')\n        option.onError(error, file)\n      })\n    },\n\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isDocx = file.name.toLowerCase().endsWith('.docx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isDocx) {\n        this.$message.error('只能上传.docx格式的Word文档!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n\n      this.uploading = true\n      this.uploadProgress = 0\n      return true\n    },\n\n    // 文件上传成功（由customUpload调用）\n    handleFileSuccess(response, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 文件上传失败（由customUpload调用）\n    handleFileError(err, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 编辑器内容变化\n    onContentChange() {\n      // 防抖处理，避免频繁请求\n      clearTimeout(this.previewTimer)\n      this.previewTimer = setTimeout(() => {\n        this.autoPreview()\n      }, 1000)\n    },\n\n    // 自动预览\n    autoPreview() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        this.previewHtml = ''\n        this.previewQuestionCount = 0\n        return\n      }\n      this.previewContent()\n    },\n\n    // 保存内容\n    saveContent() {\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent\n      }\n      saveEditedContent(data).then(response => {\n        if (response.code === 200) {\n          this.$message.success('内容保存成功')\n        } else {\n          this.$message.error(response.msg || '保存失败')\n        }\n      }).catch(error => {\n        console.error('保存内容失败', error)\n        this.$message.error('保存失败')\n      })\n    },\n\n    // 预览内容\n    previewContent() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        return\n      }\n\n      this.previewLoading = true\n      const data = {\n        content: this.editContent  // 现在直接传递文本内容，不需要HTML转换\n      }\n      \n      previewContent(data).then(response => {\n        this.previewLoading = false\n        if (response.code === 200) {\n          this.previewHtml = response.data.previewHtml || ''\n          this.previewQuestionCount = response.data.questionCount || 0\n          this.parseErrors = response.data.errors || []\n        } else {\n          this.$message.error(response.msg || '预览失败')\n        }\n      }).catch(error => {\n        this.previewLoading = false\n        console.error('预览失败', error)\n        this.$message.error('预览失败')\n      })\n    },\n\n    // 确认导入\n    confirmImport() {\n      this.importing = true\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent,\n        allowDuplicate: this.importOptions.allowDuplicate\n      }\n      \n      importFromEditor(data).then(response => {\n        this.importing = false\n        if (response.code === 200) {\n          this.importResult = response.data\n          this.nextStep()\n          this.$message.success('题目导入成功')\n        } else {\n          this.$message.error(response.msg || '导入失败')\n        }\n      }).catch(error => {\n        this.importing = false\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      })\n    },\n\n    // 下一步\n    nextStep() {\n      if (this.currentStep < 3) {\n        this.currentStep++\n        if (this.currentStep === 2) {\n          // 进入预览确认步骤时，自动预览\n          this.previewContent()\n        }\n      }\n    },\n\n    // 上一步\n    prevStep() {\n      if (this.currentStep > 0) {\n        this.currentStep--\n      }\n    },\n\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.uploadedFile = null\n      this.uploading = false\n      this.uploadProgress = 0\n      this.editContent = ''\n      this.previewHtml = ''\n      this.previewQuestionCount = 0\n      this.parseErrors = []\n      this.importing = false\n      this.importOptions = {\n        allowDuplicate: false\n      }\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    },\n\n    // 加载演示内容\n    loadDemoContent() {\n      const demoContent = `1、[单选题]计算机的CPU主要功能是什么？\nA、存储数据\nB、处理数据和控制程序执行\nC、输入数据\nD、输出数据\n答案：B\n解析：CPU（中央处理器）是计算机的核心部件，主要负责处理数据和控制程序的执行。\n\n2、[多选题]以下哪些是计算机的输入设备？\nA、键盘\nB、鼠标\nC、显示器\nD、扫描仪\n答案：A,B,D\n解析：键盘、鼠标和扫描仪都是输入设备，显示器是输出设备。\n\n3、[判断题]CPU的主频越高，计算机的性能就一定越好。\n答案：错误\n解析：CPU性能不仅取决于主频，还与架构、缓存、核心数等多个因素有关。`\n\n      this.editContent = demoContent\n      this.uploadedFile = {\n        name: '演示内容.docx',\n        response: { code: 200 }\n      }\n      this.parseErrors = []\n      this.$message.success('演示内容加载成功，可以直接进入下一步')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.rich-text-import-dialog {\n  .el-dialog {\n    margin-top: 5vh !important;\n  }\n\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n\n.import-container {\n  min-height: 600px;\n}\n\n.step-content {\n  min-height: 500px;\n  display: flex;\n  flex-direction: column;\n}\n\n.step-actions {\n  margin-top: auto;\n  padding-top: 20px;\n  border-top: 1px solid #e9ecef;\n  text-align: right;\n}\n\n/* 文件上传样式 */\n.upload-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.upload-section p {\n  margin-bottom: 30px;\n  color: #666;\n  text-align: center;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 10px 0;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n.uploaded-file {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.uploading-status {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n  text-align: center;\n}\n\n/* 编辑器样式 */\n.editor-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.editor-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.editor-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.editor-preview-layout {\n  flex: 1;\n  display: flex;\n  gap: 20px;\n  min-height: 400px;\n}\n\n.editor-panel,\n.preview-panel {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.panel-header {\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 14px;\n  color: #333;\n}\n\n.panel-tip {\n  font-size: 12px;\n  color: #666;\n}\n\n.editor-wrapper,\n.preview-wrapper {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.content-editor {\n  flex: 1;\n  min-height: 350px;\n}\n\n.preview-wrapper {\n  position: relative;\n}\n\n.preview-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n}\n\n.preview-content {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n  max-height: 350px;\n}\n\n.preview-empty {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 确认导入样式 */\n.confirm-section {\n  flex: 1;\n}\n\n.confirm-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.import-summary {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.error-section {\n  margin-bottom: 30px;\n}\n\n.error-section h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n.import-options {\n  margin-bottom: 30px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n}\n\n.import-options h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.final-preview {\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.final-preview h4 {\n  margin: 0;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  color: #333;\n}\n\n.final-preview .preview-content {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n/* 完成页面样式 */\n.result-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-section h3 {\n  margin-bottom: 30px;\n  color: #333;\n}\n\n.import-result {\n  margin-bottom: 30px;\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors {\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n/* 预览内容样式 */\n.preview-content .questions-preview {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.preview-content .question-item {\n  margin-bottom: 20px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.preview-content .question-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n  font-weight: 500;\n}\n\n.preview-content .question-number {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.preview-content .question-type {\n  color: #409eff;\n  font-weight: 500;\n}\n\n.preview-content .question-difficulty {\n  color: #666;\n  font-size: 12px;\n}\n\n.preview-content .question-content {\n  margin-bottom: 15px;\n  line-height: 1.6;\n  color: #333;\n}\n\n.preview-content .question-options {\n  margin-bottom: 15px;\n}\n\n.preview-content .option-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  margin-bottom: 8px;\n  padding: 8px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.preview-content .option-key {\n  font-weight: 500;\n  color: #409eff;\n  min-width: 20px;\n}\n\n.preview-content .option-content {\n  flex: 1;\n  line-height: 1.5;\n}\n\n.preview-content .question-answer {\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background: #e8f5e8;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.preview-content .question-analysis {\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .editor-preview-layout {\n    flex-direction: column;\n  }\n\n  .editor-panel,\n  .preview-panel {\n    min-height: 300px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;AA0NA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,QAAA;MACAP,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MAEA;MACAC,YAAA;MACAC,SAAA;MACAC,cAAA;MAEA;MACAC,WAAA;MAEA;MACAC,WAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,WAAA;MAEA;MACAC,SAAA;MACAC,aAAA;QACAC,cAAA;MACA;MACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,KAAA;IACA3B,OAAA,WAAAA,QAAA4B,GAAA;MACA,KAAAlB,aAAA,GAAAkB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,WAAA;MACA;IACA;IACAnB,aAAA,WAAAA,cAAAkB,GAAA;MACA,KAAAE,KAAA,mBAAAF,GAAA;IACA;EACA;EACAG,OAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,MAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,GAAAF,MAAA,CAAAE,IAAA;MACA,KAAAtB,SAAA;MACA,KAAAC,cAAA;MAEA,IAAAsB,4BAAA,EAAAD,IAAA,OAAA/B,MAAA,EAAAiC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAArB,SAAA;QACA,IAAAyB,QAAA,CAAAC,IAAA;UACAL,KAAA,CAAAtB,YAAA;YACAd,IAAA,EAAAqC,IAAA,CAAArC,IAAA;YACAwC,QAAA,EAAAA;UACA;UACAJ,KAAA,CAAAnB,WAAA,GAAAuB,QAAA,CAAA7B,IAAA,CAAA+B,eAAA;UACAN,KAAA,CAAAf,WAAA,GAAAmB,QAAA,CAAA7B,IAAA,CAAAiB,MAAA;UACAQ,KAAA,CAAAO,QAAA,CAAAC,OAAA;UACAT,MAAA,CAAAU,SAAA,CAAAL,QAAA,EAAAH,IAAA;QACA;UACAD,KAAA,CAAAO,QAAA,CAAAG,KAAA,CAAAN,QAAA,CAAAO,GAAA;UACAZ,MAAA,CAAAa,OAAA,KAAAC,KAAA,CAAAT,QAAA,CAAAO,GAAA,aAAAV,IAAA;QACA;MACA,GAAAa,KAAA,WAAAJ,KAAA;QACAV,KAAA,CAAArB,SAAA;QACAoC,OAAA,CAAAL,KAAA,WAAAA,KAAA;QACAV,KAAA,CAAAO,QAAA,CAAAG,KAAA;QACAX,MAAA,CAAAa,OAAA,CAAAF,KAAA,EAAAT,IAAA;MACA;IACA;IAEA;IACAe,gBAAA,WAAAA,iBAAAf,IAAA;MACA,IAAAgB,MAAA,GAAAhB,IAAA,CAAArC,IAAA,CAAAsD,WAAA,GAAAC,QAAA;MACA,IAAAC,OAAA,GAAAnB,IAAA,CAAAoB,IAAA;MAEA,KAAAJ,MAAA;QACA,KAAAV,QAAA,CAAAG,KAAA;QACA;MACA;MACA,KAAAU,OAAA;QACA,KAAAb,QAAA,CAAAG,KAAA;QACA;MACA;MAEA,KAAA/B,SAAA;MACA,KAAAC,cAAA;MACA;IACA;IAEA;IACA0C,iBAAA,WAAAA,kBAAAlB,QAAA,EAAAH,IAAA;MACA;IAAA,CACA;IAEA;IACAsB,eAAA,WAAAA,gBAAAC,GAAA,EAAAvB,IAAA;MACA;IAAA,CACA;IAEA;IACAwB,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA;MACAC,YAAA,MAAAC,YAAA;MACA,KAAAA,YAAA,GAAAC,UAAA;QACAH,MAAA,CAAAI,WAAA;MACA;IACA;IAEA;IACAA,WAAA,WAAAA,YAAA;MACA,UAAAjD,WAAA,SAAAA,WAAA,CAAAkD,IAAA;QACA,KAAAjD,WAAA;QACA,KAAAC,oBAAA;QACA;MACA;MACA,KAAAiD,cAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAA3D,IAAA;QACAL,MAAA,OAAAA,MAAA;QACAiE,OAAA,OAAAtD;MACA;MACA,IAAAuD,+BAAA,EAAA7D,IAAA,EAAA4B,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA6B,MAAA,CAAA3B,QAAA,CAAAC,OAAA;QACA;UACA0B,MAAA,CAAA3B,QAAA,CAAAG,KAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA,GAAAG,KAAA,WAAAJ,KAAA;QACAK,OAAA,CAAAL,KAAA,WAAAA,KAAA;QACAwB,MAAA,CAAA3B,QAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACAsB,cAAA,WAAAA,eAAA;MAAA,IAAAK,MAAA;MACA,UAAAxD,WAAA,SAAAA,WAAA,CAAAkD,IAAA;QACA;MACA;MAEA,KAAA/C,cAAA;MACA,IAAAT,IAAA;QACA4D,OAAA,OAAAtD,WAAA;MACA;MAEA,IAAAmD,4BAAA,EAAAzD,IAAA,EAAA4B,IAAA,WAAAC,QAAA;QACAiC,MAAA,CAAArD,cAAA;QACA,IAAAoB,QAAA,CAAAC,IAAA;UACAgC,MAAA,CAAAvD,WAAA,GAAAsB,QAAA,CAAA7B,IAAA,CAAAO,WAAA;UACAuD,MAAA,CAAAtD,oBAAA,GAAAqB,QAAA,CAAA7B,IAAA,CAAA+D,aAAA;UACAD,MAAA,CAAApD,WAAA,GAAAmB,QAAA,CAAA7B,IAAA,CAAAiB,MAAA;QACA;UACA6C,MAAA,CAAA9B,QAAA,CAAAG,KAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA,GAAAG,KAAA,WAAAJ,KAAA;QACA2B,MAAA,CAAArD,cAAA;QACA+B,OAAA,CAAAL,KAAA,SAAAA,KAAA;QACA2B,MAAA,CAAA9B,QAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACA6B,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAtD,SAAA;MACA,IAAAX,IAAA;QACAL,MAAA,OAAAA,MAAA;QACAiE,OAAA,OAAAtD,WAAA;QACAO,cAAA,OAAAD,aAAA,CAAAC;MACA;MAEA,IAAAqD,8BAAA,EAAAlE,IAAA,EAAA4B,IAAA,WAAAC,QAAA;QACAoC,MAAA,CAAAtD,SAAA;QACA,IAAAkB,QAAA,CAAAC,IAAA;UACAmC,MAAA,CAAAnD,YAAA,GAAAe,QAAA,CAAA7B,IAAA;UACAiE,MAAA,CAAAE,QAAA;UACAF,MAAA,CAAAjC,QAAA,CAAAC,OAAA;QACA;UACAgC,MAAA,CAAAjC,QAAA,CAAAG,KAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA,GAAAG,KAAA,WAAAJ,KAAA;QACA8B,MAAA,CAAAtD,SAAA;QACA6B,OAAA,CAAAL,KAAA,SAAAA,KAAA;QACA8B,MAAA,CAAAjC,QAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACAgC,QAAA,WAAAA,SAAA;MACA,SAAAjE,WAAA;QACA,KAAAA,WAAA;QACA,SAAAA,WAAA;UACA;UACA,KAAAuD,cAAA;QACA;MACA;IACA;IAEA;IACAW,QAAA,WAAAA,SAAA;MACA,SAAAlE,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACAmE,cAAA,WAAAA,eAAA;MACA,KAAAhD,KAAA;MACA,KAAAiD,WAAA;IACA;IAEA;IACAlD,WAAA,WAAAA,YAAA;MACA,KAAAlB,WAAA;MACA,KAAAC,YAAA;MACA,KAAAC,SAAA;MACA,KAAAC,cAAA;MACA,KAAAC,WAAA;MACA,KAAAC,WAAA;MACA,KAAAC,oBAAA;MACA,KAAAE,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,aAAA;QACAC,cAAA;MACA;MACA,KAAAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAqD,WAAA,WAAAA,YAAA;MACA,KAAArE,aAAA;IACA;IAEA;IACAsE,eAAA,WAAAA,gBAAA;MACA,IAAAC,WAAA,0xCAkBA;MAEA,KAAAlE,WAAA,GAAAkE,WAAA;MACA,KAAArE,YAAA;QACAd,IAAA;QACAwC,QAAA;UAAAC,IAAA;QAAA;MACA;MACA,KAAApB,WAAA;MACA,KAAAsB,QAAA,CAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}