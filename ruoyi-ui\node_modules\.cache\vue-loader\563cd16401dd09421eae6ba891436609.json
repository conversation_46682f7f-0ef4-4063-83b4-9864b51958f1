{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJwppbXBvcnQgeyB1cGxvYWRBbmRQYXJzZSwgc2F2ZUVkaXRlZENvbnRlbnQsIHByZXZpZXdDb250ZW50LCBpbXBvcnRGcm9tRWRpdG9yIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uQmFuaycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUmljaFRleHRJbXBvcnQiLAogIHByb3BzOiB7CiAgICB2aXNpYmxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgYmFua0lkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgYmFua05hbWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAn6aKY5bqTJwogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBjdXJyZW50U3RlcDogMCwKICAgICAgCiAgICAgIC8vIOaWh+S7tuS4iuS8oOebuOWFswogICAgICB1cGxvYWRlZEZpbGU6IG51bGwsCiAgICAgIHVwbG9hZGluZzogZmFsc2UsCiAgICAgIHVwbG9hZFByb2dyZXNzOiAwLAogICAgICAKICAgICAgLy8g57yW6L6R5Zmo55u45YWzCiAgICAgIGVkaXRDb250ZW50OiAnJywKICAgICAgCiAgICAgIC8vIOmihOiniOebuOWFswogICAgICBwcmV2aWV3SHRtbDogJycsCiAgICAgIHByZXZpZXdRdWVzdGlvbkNvdW50OiAwLAogICAgICBwcmV2aWV3TG9hZGluZzogZmFsc2UsCiAgICAgIHBhcnNlRXJyb3JzOiBbXSwKICAgICAgCiAgICAgIC8vIOWvvOWFpeebuOWFswogICAgICBpbXBvcnRpbmc6IGZhbHNlLAogICAgICBpbXBvcnRPcHRpb25zOiB7CiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0sCiAgICAgIGltcG9ydFJlc3VsdDogewogICAgICAgIHN1Y2Nlc3NDb3VudDogMCwKICAgICAgICBmYWlsQ291bnQ6IDAsCiAgICAgICAgZXJyb3JzOiBbXQogICAgICB9CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgdmlzaWJsZSh2YWwpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdmFsCiAgICAgIGlmICh2YWwpIHsKICAgICAgICB0aGlzLnJlc2V0SW1wb3J0KCkKICAgICAgfQogICAgfSwKICAgIGRpYWxvZ1Zpc2libGUodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgdmFsKQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLy8g6Ieq5a6a5LmJ5LiK5Lyg5pa55rOVCiAgICBjdXN0b21VcGxvYWQob3B0aW9uKSB7CiAgICAgIGNvbnN0IGZpbGUgPSBvcHRpb24uZmlsZQogICAgICB0aGlzLnVwbG9hZGluZyA9IHRydWUKICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDAKCiAgICAgIHVwbG9hZEFuZFBhcnNlKGZpbGUsIHRoaXMuYmFua0lkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnVwbG9hZGluZyA9IGZhbHNlCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy51cGxvYWRlZEZpbGUgPSB7CiAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSwKICAgICAgICAgICAgcmVzcG9uc2U6IHJlc3BvbnNlCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmVkaXRDb250ZW50ID0gcmVzcG9uc2UuZGF0YS5lZGl0YWJsZUNvbnRlbnQgfHwgJycKICAgICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSByZXNwb25zZS5kYXRhLmVycm9ycyB8fCBbXQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofmoaPkuIrkvKDmiJDlip8nKQogICAgICAgICAgb3B0aW9uLm9uU3VjY2VzcyhyZXNwb25zZSwgZmlsZSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+aho+S4iuS8oOWksei0pScpCiAgICAgICAgICBvcHRpb24ub25FcnJvcihuZXcgRXJyb3IocmVzcG9uc2UubXNnIHx8ICfkuIrkvKDlpLHotKUnKSwgZmlsZSkKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICB0aGlzLnVwbG9hZGluZyA9IGZhbHNlCiAgICAgICAgY29uc29sZS5lcnJvcign5paH5Lu25LiK5Lyg5aSx6LSlJywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25LiK5Lyg5aSx6LSlJykKICAgICAgICBvcHRpb24ub25FcnJvcihlcnJvciwgZmlsZSkKICAgICAgfSkKICAgIH0sCgogICAgLy8g5paH5Lu25LiK5Lyg5YmN6aqM6K+BCiAgICBiZWZvcmVGaWxlVXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgaXNEb2N4ID0gZmlsZS5uYW1lLnRvTG93ZXJDYXNlKCkuZW5kc1dpdGgoJy5kb2N4JykKICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTAKCiAgICAgIGlmICghaXNEb2N4KSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5LygLmRvY3jmoLzlvI/nmoRXb3Jk5paH5qGjIScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KICAgICAgaWYgKCFpc0x0MTBNKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HMTBNQiEnKQogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CgogICAgICB0aGlzLnVwbG9hZGluZyA9IHRydWUKICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDAKICAgICAgcmV0dXJuIHRydWUKICAgIH0sCgogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5Yqf77yI55SxY3VzdG9tVXBsb2Fk6LCD55So77yJCiAgICBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSkgewogICAgICAvLyDov5nkuKrmlrnms5XnjrDlnKjnlLFjdXN0b21VcGxvYWTlpITnkIbvvIzkv53nlZnku6XpmLLpnIDopoEKICAgIH0sCgogICAgLy8g5paH5Lu25LiK5Lyg5aSx6LSl77yI55SxY3VzdG9tVXBsb2Fk6LCD55So77yJCiAgICBoYW5kbGVGaWxlRXJyb3IoZXJyLCBmaWxlKSB7CiAgICAgIC8vIOi/meS4quaWueazleeOsOWcqOeUsWN1c3RvbVVwbG9hZOWkhOeQhu+8jOS/neeVmeS7pemYsumcgOimgQogICAgfSwKCiAgICAvLyDnvJbovpHlmajlhoXlrrnlj5jljJYKICAgIG9uQ29udGVudENoYW5nZSgpIHsKICAgICAgLy8g6Ziy5oqW5aSE55CG77yM6YG/5YWN6aKR57mB6K+35rGCCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnByZXZpZXdUaW1lcikKICAgICAgdGhpcy5wcmV2aWV3VGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLmF1dG9QcmV2aWV3KCkKICAgICAgfSwgMTAwMCkKICAgIH0sCgogICAgLy8g6Ieq5Yqo6aKE6KeICiAgICBhdXRvUHJldmlldygpIHsKICAgICAgaWYgKCF0aGlzLmVkaXRDb250ZW50IHx8IHRoaXMuZWRpdENvbnRlbnQudHJpbSgpID09PSAnJykgewogICAgICAgIHRoaXMucHJldmlld0h0bWwgPSAnJwogICAgICAgIHRoaXMucHJldmlld1F1ZXN0aW9uQ291bnQgPSAwCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgdGhpcy5wcmV2aWV3Q29udGVudCgpCiAgICB9LAoKICAgIC8vIOS/neWtmOWGheWuuQogICAgc2F2ZUNvbnRlbnQoKSB7CiAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZCwKICAgICAgICBjb250ZW50OiB0aGlzLmVkaXRDb250ZW50CiAgICAgIH0KICAgICAgc2F2ZUVkaXRlZENvbnRlbnQoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflhoXlrrnkv53lrZjmiJDlip8nKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5L+d5a2Y5aSx6LSlJykKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCfkv53lrZjlhoXlrrnlpLHotKUnLCBlcnJvcikKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkv53lrZjlpLHotKUnKQogICAgICB9KQogICAgfSwKCiAgICAvLyDpooTop4jlhoXlrrkKICAgIHByZXZpZXdDb250ZW50KCkgewogICAgICBpZiAoIXRoaXMuZWRpdENvbnRlbnQgfHwgdGhpcy5lZGl0Q29udGVudC50cmltKCkgPT09ICcnKSB7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMucHJldmlld0xvYWRpbmcgPSB0cnVlCiAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgY29udGVudDogdGhpcy5lZGl0Q29udGVudCAgLy8g546w5Zyo55u05o6l5Lyg6YCS5paH5pys5YaF5a6577yM5LiN6ZyA6KaBSFRNTOi9rOaNogogICAgICB9CiAgICAgIAogICAgICBwcmV2aWV3Q29udGVudChkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnByZXZpZXdMb2FkaW5nID0gZmFsc2UKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLnByZXZpZXdIdG1sID0gcmVzcG9uc2UuZGF0YS5wcmV2aWV3SHRtbCB8fCAnJwogICAgICAgICAgdGhpcy5wcmV2aWV3UXVlc3Rpb25Db3VudCA9IHJlc3BvbnNlLmRhdGEucXVlc3Rpb25Db3VudCB8fCAwCiAgICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcmVzcG9uc2UuZGF0YS5lcnJvcnMgfHwgW10KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+mihOiniOWksei0pScpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy5wcmV2aWV3TG9hZGluZyA9IGZhbHNlCiAgICAgICAgY29uc29sZS5lcnJvcign6aKE6KeI5aSx6LSlJywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6aKE6KeI5aSx6LSlJykKICAgICAgfSkKICAgIH0sCgogICAgLy8g56Gu6K6k5a+85YWlCiAgICBjb25maXJtSW1wb3J0KCkgewogICAgICB0aGlzLmltcG9ydGluZyA9IHRydWUKICAgICAgY29uc3QgZGF0YSA9IHsKICAgICAgICBiYW5rSWQ6IHRoaXMuYmFua0lkLAogICAgICAgIGNvbnRlbnQ6IHRoaXMuZWRpdENvbnRlbnQsCiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IHRoaXMuaW1wb3J0T3B0aW9ucy5hbGxvd0R1cGxpY2F0ZQogICAgICB9CiAgICAgIAogICAgICBpbXBvcnRGcm9tRWRpdG9yKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuaW1wb3J0aW5nID0gZmFsc2UKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLmltcG9ydFJlc3VsdCA9IHJlc3BvbnNlLmRhdGEKICAgICAgICAgIHRoaXMubmV4dFN0ZXAoKQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfpopjnm67lr7zlhaXmiJDlip8nKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5a+85YWl5aSx6LSlJykKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICB0aGlzLmltcG9ydGluZyA9IGZhbHNlCiAgICAgICAgY29uc29sZS5lcnJvcign5a+85YWl5aSx6LSlJywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a+85YWl5aSx6LSlJykKICAgICAgfSkKICAgIH0sCgogICAgLy8g5LiL5LiA5q2lCiAgICBuZXh0U3RlcCgpIHsKICAgICAgaWYgKHRoaXMuY3VycmVudFN0ZXAgPCAzKSB7CiAgICAgICAgdGhpcy5jdXJyZW50U3RlcCsrCiAgICAgICAgaWYgKHRoaXMuY3VycmVudFN0ZXAgPT09IDIpIHsKICAgICAgICAgIC8vIOi/m+WFpemihOiniOehruiupOatpemqpOaXtu+8jOiHquWKqOmihOiniAogICAgICAgICAgdGhpcy5wcmV2aWV3Q29udGVudCgpCiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8vIOS4iuS4gOatpQogICAgcHJldlN0ZXAoKSB7CiAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwID4gMCkgewogICAgICAgIHRoaXMuY3VycmVudFN0ZXAtLQogICAgICB9CiAgICB9LAoKICAgIC8vIOWujOaIkOWvvOWFpQogICAgaGFuZGxlQ29tcGxldGUoKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3N1Y2Nlc3MnKQogICAgICB0aGlzLmhhbmRsZUNsb3NlKCkKICAgIH0sCgogICAgLy8g6YeN572u5a+85YWlCiAgICByZXNldEltcG9ydCgpIHsKICAgICAgdGhpcy5jdXJyZW50U3RlcCA9IDAKICAgICAgdGhpcy51cGxvYWRlZEZpbGUgPSBudWxsCiAgICAgIHRoaXMudXBsb2FkaW5nID0gZmFsc2UKICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDAKICAgICAgdGhpcy5lZGl0Q29udGVudCA9ICcnCiAgICAgIHRoaXMucHJldmlld0h0bWwgPSAnJwogICAgICB0aGlzLnByZXZpZXdRdWVzdGlvbkNvdW50ID0gMAogICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgdGhpcy5pbXBvcnRpbmcgPSBmYWxzZQogICAgICB0aGlzLmltcG9ydE9wdGlvbnMgPSB7CiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0KICAgICAgdGhpcy5pbXBvcnRSZXN1bHQgPSB7CiAgICAgICAgc3VjY2Vzc0NvdW50OiAwLAogICAgICAgIGZhaWxDb3VudDogMCwKICAgICAgICBlcnJvcnM6IFtdCiAgICAgIH0KICAgIH0sCgogICAgLy8g5YWz6Zet5a+56K+d5qGGCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgIH0sCgogICAgLy8g5Yqg6L295ryU56S65YaF5a65CiAgICBsb2FkRGVtb0NvbnRlbnQoKSB7CiAgICAgIGNvbnN0IGRlbW9Db250ZW50ID0gYDHjgIFb5Y2V6YCJ6aKYXeiuoeeul+acuueahENQVeS4u+imgeWKn+iDveaYr+S7gOS5iO+8nwpB44CB5a2Y5YKo5pWw5o2uCkLjgIHlpITnkIbmlbDmja7lkozmjqfliLbnqIvluo/miafooYwKQ+OAgei+k+WFpeaVsOaNrgpE44CB6L6T5Ye65pWw5o2uCuetlOahiO+8mkIK6Kej5p6Q77yaQ1BV77yI5Lit5aSu5aSE55CG5Zmo77yJ5piv6K6h566X5py655qE5qC45b+D6YOo5Lu277yM5Li76KaB6LSf6LSj5aSE55CG5pWw5o2u5ZKM5o6n5Yi256iL5bqP55qE5omn6KGM44CCCgoy44CBW+WkmumAiemimF3ku6XkuIvlk6rkupvmmK/orqHnrpfmnLrnmoTovpPlhaXorr7lpIfvvJ8KQeOAgemUruebmApC44CB6byg5qCHCkPjgIHmmL7npLrlmagKROOAgeaJq+aPj+S7qgrnrZTmoYjvvJpBLEIsRArop6PmnpDvvJrplK7nm5jjgIHpvKDmoIflkozmiavmj4/ku6rpg73mmK/ovpPlhaXorr7lpIfvvIzmmL7npLrlmajmmK/ovpPlh7rorr7lpIfjgIIKCjPjgIFb5Yik5pat6aKYXUNQVeeahOS4u+mikei2iumrmO+8jOiuoeeul+acuueahOaAp+iDveWwseS4gOWumui2iuWlveOAggrnrZTmoYjvvJrplJnor68K6Kej5p6Q77yaQ1BV5oCn6IO95LiN5LuF5Y+W5Yaz5LqO5Li76aKR77yM6L+Y5LiO5p625p6E44CB57yT5a2Y44CB5qC45b+D5pWw562J5aSa5Liq5Zug57Sg5pyJ5YWz44CCYAoKICAgICAgdGhpcy5lZGl0Q29udGVudCA9IGRlbW9Db250ZW50CiAgICAgIHRoaXMudXBsb2FkZWRGaWxlID0gewogICAgICAgIG5hbWU6ICfmvJTnpLrlhoXlrrkuZG9jeCcsCiAgICAgICAgcmVzcG9uc2U6IHsgY29kZTogMjAwIH0KICAgICAgfQogICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmvJTnpLrlhoXlrrnliqDovb3miJDlip/vvIzlj6/ku6Xnm7TmjqXov5vlhaXkuIvkuIDmraUnKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["RichTextImport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0NA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RichTextImport.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"题库文档导入\"\n    :visible.sync=\"dialogVisible\"\n    width=\"95%\"\n    :before-close=\"handleClose\"\n    append-to-body\n    class=\"rich-text-import-dialog\"\n  >\n    <div class=\"import-container\">\n      <!-- 步骤指示器 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"上传文档\"></el-step>\n        <el-step title=\"编辑内容\"></el-step>\n        <el-step title=\"预览确认\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 文档上传 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <h3>上传Word文档</h3>\n          <p>请上传包含题目的Word文档(.docx格式)，系统将自动解析文档内容</p>\n\n          <div class=\"demo-section\">\n            <el-button size=\"small\" type=\"text\" @click=\"loadDemoContent\">\n              <i class=\"el-icon-magic-stick\"></i> 加载演示内容\n            </el-button>\n            <span class=\"demo-tip\">或者点击加载演示内容快速体验功能</span>\n          </div>\n\n          <el-upload\n            ref=\"fileUpload\"\n            :http-request=\"customUpload\"\n            :on-success=\"handleFileSuccess\"\n            :on-error=\"handleFileError\"\n            :before-upload=\"beforeFileUpload\"\n            :show-file-list=\"false\"\n            :accept=\".docx\"\n            drag\n          >\n            <div class=\"upload-area\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"upload-text\">\n                <p>将Word文档拖到此处，或<em>点击上传</em></p>\n                <p class=\"upload-tip\">支持 .docx 格式文件，文件大小不超过10MB</p>\n              </div>\n            </div>\n          </el-upload>\n\n          <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n            <el-alert\n              :title=\"`已上传文件：${uploadedFile.name}`\"\n              type=\"success\"\n              :closable=\"false\"\n              show-icon\n            />\n          </div>\n\n          <div v-if=\"uploading\" class=\"uploading-status\">\n            <el-progress :percentage=\"50\" :show-text=\"false\"></el-progress>\n            <p>正在上传并解析文档...</p>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"handleClose\">取消</el-button>\n          <el-button type=\"primary\" :disabled=\"!uploadedFile\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 富文本编辑 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"editor-container\">\n          <div class=\"editor-header\">\n            <h3>编辑文档内容</h3>\n            <div class=\"editor-actions\">\n              <el-button size=\"small\" @click=\"saveContent\">保存内容</el-button>\n              <el-button size=\"small\" type=\"primary\" @click=\"previewContent\">实时预览</el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-preview-layout\">\n            <!-- 左侧富文本编辑器 -->\n            <div class=\"editor-panel\">\n              <div class=\"panel-header\">\n                <h4>编辑区域</h4>\n                <span class=\"panel-tip\">您可以在此编辑题目内容</span>\n              </div>\n              <div class=\"editor-wrapper\">\n                <el-input\n                  v-model=\"editContent\"\n                  type=\"textarea\"\n                  :rows=\"15\"\n                  placeholder=\"请编辑题目内容...\"\n                  @input=\"onContentChange\"\n                  class=\"content-editor\"\n                />\n              </div>\n            </div>\n\n            <!-- 右侧预览区域 -->\n            <div class=\"preview-panel\">\n              <div class=\"panel-header\">\n                <h4>实时预览</h4>\n                <span class=\"panel-tip\">题目解析结果：{{ previewQuestionCount }}道题目</span>\n              </div>\n              <div class=\"preview-wrapper\">\n                <div v-if=\"previewLoading\" class=\"preview-loading\">\n                  <el-loading text=\"正在解析预览...\"></el-loading>\n                </div>\n                <div v-else-if=\"previewHtml\" class=\"preview-content\" v-html=\"previewHtml\"></div>\n                <div v-else class=\"preview-empty\">\n                  <el-empty description=\"暂无预览内容，请编辑左侧内容\"></el-empty>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :disabled=\"!editContent\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 预览确认 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"confirm-section\">\n          <h3>确认导入</h3>\n          <div class=\"import-summary\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析题目数量\" :value=\"previewQuestionCount\" suffix=\"道\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析错误\" :value=\"parseErrors.length\" suffix=\"个\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"目标题库\" :value=\"bankName\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <h4>解析错误信息</h4>\n            <el-alert\n              v-for=\"(error, index) in parseErrors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"warning\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n\n          <div class=\"import-options\">\n            <h4>导入选项</h4>\n            <el-checkbox v-model=\"importOptions.allowDuplicate\">允许导入重复题目</el-checkbox>\n          </div>\n\n          <div class=\"final-preview\">\n            <h4>最终预览</h4>\n            <div class=\"preview-content\" v-html=\"previewHtml\"></div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :loading=\"importing\" @click=\"confirmImport\">确认导入</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 64px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          \n          <div class=\"import-result\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"成功导入\" :value=\"importResult.successCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"导入失败\" :value=\"importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"总计处理\" :value=\"importResult.successCount + importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"import-errors\">\n            <h4>导入错误详情</h4>\n            <el-alert\n              v-for=\"(error, index) in importResult.errors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"error\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { uploadAndParse, saveEditedContent, previewContent, importFromEditor } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"RichTextImport\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    bankName: {\n      type: String,\n      default: '题库'\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n      \n      // 文件上传相关\n      uploadedFile: null,\n      uploading: false,\n      uploadProgress: 0,\n      \n      // 编辑器相关\n      editContent: '',\n      \n      // 预览相关\n      previewHtml: '',\n      previewQuestionCount: 0,\n      previewLoading: false,\n      parseErrors: [],\n      \n      // 导入相关\n      importing: false,\n      importOptions: {\n        allowDuplicate: false\n      },\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  methods: {\n    // 自定义上传方法\n    customUpload(option) {\n      const file = option.file\n      this.uploading = true\n      this.uploadProgress = 0\n\n      uploadAndParse(file, this.bankId).then(response => {\n        this.uploading = false\n        if (response.code === 200) {\n          this.uploadedFile = {\n            name: file.name,\n            response: response\n          }\n          this.editContent = response.data.editableContent || ''\n          this.parseErrors = response.data.errors || []\n          this.$message.success('文档上传成功')\n          option.onSuccess(response, file)\n        } else {\n          this.$message.error(response.msg || '文档上传失败')\n          option.onError(new Error(response.msg || '上传失败'), file)\n        }\n      }).catch(error => {\n        this.uploading = false\n        console.error('文件上传失败', error)\n        this.$message.error('文件上传失败')\n        option.onError(error, file)\n      })\n    },\n\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isDocx = file.name.toLowerCase().endsWith('.docx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isDocx) {\n        this.$message.error('只能上传.docx格式的Word文档!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n\n      this.uploading = true\n      this.uploadProgress = 0\n      return true\n    },\n\n    // 文件上传成功（由customUpload调用）\n    handleFileSuccess(response, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 文件上传失败（由customUpload调用）\n    handleFileError(err, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 编辑器内容变化\n    onContentChange() {\n      // 防抖处理，避免频繁请求\n      clearTimeout(this.previewTimer)\n      this.previewTimer = setTimeout(() => {\n        this.autoPreview()\n      }, 1000)\n    },\n\n    // 自动预览\n    autoPreview() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        this.previewHtml = ''\n        this.previewQuestionCount = 0\n        return\n      }\n      this.previewContent()\n    },\n\n    // 保存内容\n    saveContent() {\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent\n      }\n      saveEditedContent(data).then(response => {\n        if (response.code === 200) {\n          this.$message.success('内容保存成功')\n        } else {\n          this.$message.error(response.msg || '保存失败')\n        }\n      }).catch(error => {\n        console.error('保存内容失败', error)\n        this.$message.error('保存失败')\n      })\n    },\n\n    // 预览内容\n    previewContent() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        return\n      }\n\n      this.previewLoading = true\n      const data = {\n        content: this.editContent  // 现在直接传递文本内容，不需要HTML转换\n      }\n      \n      previewContent(data).then(response => {\n        this.previewLoading = false\n        if (response.code === 200) {\n          this.previewHtml = response.data.previewHtml || ''\n          this.previewQuestionCount = response.data.questionCount || 0\n          this.parseErrors = response.data.errors || []\n        } else {\n          this.$message.error(response.msg || '预览失败')\n        }\n      }).catch(error => {\n        this.previewLoading = false\n        console.error('预览失败', error)\n        this.$message.error('预览失败')\n      })\n    },\n\n    // 确认导入\n    confirmImport() {\n      this.importing = true\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent,\n        allowDuplicate: this.importOptions.allowDuplicate\n      }\n      \n      importFromEditor(data).then(response => {\n        this.importing = false\n        if (response.code === 200) {\n          this.importResult = response.data\n          this.nextStep()\n          this.$message.success('题目导入成功')\n        } else {\n          this.$message.error(response.msg || '导入失败')\n        }\n      }).catch(error => {\n        this.importing = false\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      })\n    },\n\n    // 下一步\n    nextStep() {\n      if (this.currentStep < 3) {\n        this.currentStep++\n        if (this.currentStep === 2) {\n          // 进入预览确认步骤时，自动预览\n          this.previewContent()\n        }\n      }\n    },\n\n    // 上一步\n    prevStep() {\n      if (this.currentStep > 0) {\n        this.currentStep--\n      }\n    },\n\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.uploadedFile = null\n      this.uploading = false\n      this.uploadProgress = 0\n      this.editContent = ''\n      this.previewHtml = ''\n      this.previewQuestionCount = 0\n      this.parseErrors = []\n      this.importing = false\n      this.importOptions = {\n        allowDuplicate: false\n      }\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    },\n\n    // 加载演示内容\n    loadDemoContent() {\n      const demoContent = `1、[单选题]计算机的CPU主要功能是什么？\nA、存储数据\nB、处理数据和控制程序执行\nC、输入数据\nD、输出数据\n答案：B\n解析：CPU（中央处理器）是计算机的核心部件，主要负责处理数据和控制程序的执行。\n\n2、[多选题]以下哪些是计算机的输入设备？\nA、键盘\nB、鼠标\nC、显示器\nD、扫描仪\n答案：A,B,D\n解析：键盘、鼠标和扫描仪都是输入设备，显示器是输出设备。\n\n3、[判断题]CPU的主频越高，计算机的性能就一定越好。\n答案：错误\n解析：CPU性能不仅取决于主频，还与架构、缓存、核心数等多个因素有关。`\n\n      this.editContent = demoContent\n      this.uploadedFile = {\n        name: '演示内容.docx',\n        response: { code: 200 }\n      }\n      this.parseErrors = []\n      this.$message.success('演示内容加载成功，可以直接进入下一步')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.rich-text-import-dialog {\n  .el-dialog {\n    margin-top: 5vh !important;\n  }\n\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n\n.import-container {\n  min-height: 600px;\n}\n\n.step-content {\n  min-height: 500px;\n  display: flex;\n  flex-direction: column;\n}\n\n.step-actions {\n  margin-top: auto;\n  padding-top: 20px;\n  border-top: 1px solid #e9ecef;\n  text-align: right;\n}\n\n/* 文件上传样式 */\n.upload-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.upload-section p {\n  margin-bottom: 30px;\n  color: #666;\n  text-align: center;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 10px 0;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n.uploaded-file {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.uploading-status {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n  text-align: center;\n}\n\n/* 编辑器样式 */\n.editor-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.editor-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.editor-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.editor-preview-layout {\n  flex: 1;\n  display: flex;\n  gap: 20px;\n  min-height: 400px;\n}\n\n.editor-panel,\n.preview-panel {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.panel-header {\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 14px;\n  color: #333;\n}\n\n.panel-tip {\n  font-size: 12px;\n  color: #666;\n}\n\n.editor-wrapper,\n.preview-wrapper {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.content-editor {\n  flex: 1;\n  min-height: 350px;\n}\n\n.preview-wrapper {\n  position: relative;\n}\n\n.preview-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n}\n\n.preview-content {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n  max-height: 350px;\n}\n\n.preview-empty {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 确认导入样式 */\n.confirm-section {\n  flex: 1;\n}\n\n.confirm-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.import-summary {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.error-section {\n  margin-bottom: 30px;\n}\n\n.error-section h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n.import-options {\n  margin-bottom: 30px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n}\n\n.import-options h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.final-preview {\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.final-preview h4 {\n  margin: 0;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  color: #333;\n}\n\n.final-preview .preview-content {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n/* 完成页面样式 */\n.result-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-section h3 {\n  margin-bottom: 30px;\n  color: #333;\n}\n\n.import-result {\n  margin-bottom: 30px;\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors {\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n/* 预览内容样式 */\n.preview-content .questions-preview {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.preview-content .question-item {\n  margin-bottom: 20px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.preview-content .question-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n  font-weight: 500;\n}\n\n.preview-content .question-number {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.preview-content .question-type {\n  color: #409eff;\n  font-weight: 500;\n}\n\n.preview-content .question-difficulty {\n  color: #666;\n  font-size: 12px;\n}\n\n.preview-content .question-content {\n  margin-bottom: 15px;\n  line-height: 1.6;\n  color: #333;\n}\n\n.preview-content .question-options {\n  margin-bottom: 15px;\n}\n\n.preview-content .option-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  margin-bottom: 8px;\n  padding: 8px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.preview-content .option-key {\n  font-weight: 500;\n  color: #409eff;\n  min-width: 20px;\n}\n\n.preview-content .option-content {\n  flex: 1;\n  line-height: 1.5;\n}\n\n.preview-content .question-answer {\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background: #e8f5e8;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.preview-content .question-analysis {\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .editor-preview-layout {\n    flex-direction: column;\n  }\n\n  .editor-panel,\n  .preview-panel {\n    min-height: 300px;\n  }\n}\n</style>\n"]}]}