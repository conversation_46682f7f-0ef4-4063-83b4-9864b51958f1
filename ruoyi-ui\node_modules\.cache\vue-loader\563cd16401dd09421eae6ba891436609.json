{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJwppbXBvcnQgeyB1cGxvYWRBbmRQYXJzZSwgc2F2ZUVkaXRlZENvbnRlbnQsIHByZXZpZXdDb250ZW50LCBpbXBvcnRGcm9tRWRpdG9yIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uQmFuaycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUmljaFRleHRJbXBvcnQiLAogIHByb3BzOiB7CiAgICB2aXNpYmxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgYmFua0lkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgYmFua05hbWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAn6aKY5bqTJwogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBjdXJyZW50U3RlcDogMCwKCiAgICAgIC8vIOagh+etvumhteebuOWFswogICAgICBhY3RpdmVUYWI6ICd1cGxvYWQnLAoKICAgICAgLy8g5paH5Lu25LiK5Lyg55u45YWzCiAgICAgIHVwbG9hZGVkRmlsZTogbnVsbCwKICAgICAgdXBsb2FkaW5nOiBmYWxzZSwKICAgICAgdXBsb2FkUHJvZ3Jlc3M6IDAsCiAgICAgIAogICAgICAvLyDnvJbovpHlmajnm7jlhbMKICAgICAgZWRpdENvbnRlbnQ6ICcnLAogICAgICAKICAgICAgLy8g6aKE6KeI55u45YWzCiAgICAgIHByZXZpZXdIdG1sOiAnJywKICAgICAgcHJldmlld1F1ZXN0aW9uQ291bnQ6IDAsCiAgICAgIHByZXZpZXdMb2FkaW5nOiBmYWxzZSwKICAgICAgcGFyc2VFcnJvcnM6IFtdLAogICAgICAKICAgICAgLy8g5a+85YWl55u45YWzCiAgICAgIGltcG9ydGluZzogZmFsc2UsCiAgICAgIGltcG9ydE9wdGlvbnM6IHsKICAgICAgICBhbGxvd0R1cGxpY2F0ZTogZmFsc2UKICAgICAgfSwKICAgICAgaW1wb3J0UmVzdWx0OiB7CiAgICAgICAgc3VjY2Vzc0NvdW50OiAwLAogICAgICAgIGZhaWxDb3VudDogMCwKICAgICAgICBlcnJvcnM6IFtdCiAgICAgIH0sCgogICAgICAvLyDnpLrkvovlhoXlrrkKICAgICAgZXhhbXBsZUNvbnRlbnQ6IGAx44CBW+WNlemAiemimF3orqHnrpfmnLrnmoRDUFXkuLvopoHlip/og73mmK/ku4DkuYjvvJ8KQeOAgeWtmOWCqOaVsOaNrgpC44CB5aSE55CG5pWw5o2u5ZKM5o6n5Yi256iL5bqP5omn6KGMCkPjgIHovpPlhaXmlbDmja4KROOAgei+k+WHuuaVsOaNrgrnrZTmoYjvvJpCCuino+aekO+8mkNQVe+8iOS4reWkruWkhOeQhuWZqO+8ieaYr+iuoeeul+acuueahOaguOW/g+mDqOS7tu+8jOS4u+imgei0n+i0o+WkhOeQhuaVsOaNruWSjOaOp+WItueoi+W6j+eahOaJp+ihjOOAggoKMuOAgVvlpJrpgInpophd5Lul5LiL5ZOq5Lqb5piv6K6h566X5py655qE6L6T5YWl6K6+5aSH77yfCkHjgIHplK7nm5gKQuOAgem8oOaghwpD44CB5pi+56S65ZmoCkTjgIHmiavmj4/ku6oKReOAgeaJk+WNsOacugrnrZTmoYjvvJpBLEIsRArop6PmnpDvvJrplK7nm5jjgIHpvKDmoIflkozmiavmj4/ku6rpg73mmK/ovpPlhaXorr7lpIfvvIzmmL7npLrlmajlkozmiZPljbDmnLrmmK/ovpPlh7rorr7lpIfjgIIKCjPjgIFb5Yik5pat6aKYXUNQVeeahOS4u+mikei2iumrmO+8jOiuoeeul+acuueahOaAp+iDveWwseS4gOWumui2iuWlveOAggrnrZTmoYjvvJrplJnor68K6Kej5p6Q77yaQ1BV5oCn6IO95LiN5LuF5Y+W5Yaz5LqO5Li76aKR77yM6L+Y5LiO5p625p6E44CB57yT5a2Y44CB5qC45b+D5pWw562J5aSa5Liq5Zug57Sg5pyJ5YWz44CCCgo044CBW+WNlemAiemimF3ku6XkuIvlk6rkuKrkuI3mmK/mk43kvZzns7vnu5/vvJ8KQeOAgVdpbmRvd3MKQuOAgUxpbnV4CkPjgIFPZmZpY2UKROOAgW1hY09TCuetlOahiO+8mkMK6Kej5p6Q77yaT2ZmaWNl5piv5Yqe5YWs6L2v5Lu25aWX5Lu277yM5LiN5piv5pON5L2c57O757uf44CCV2luZG93c+OAgUxpbnV45ZKMbWFjT1Ppg73mmK/mk43kvZzns7vnu5/jgIIKCjXjgIFb5Yik5pat6aKYXeiuoeeul+acuueXheavkuWPr+S7pemAmui/h+e9kee7nOS8oOaSreOAggrnrZTmoYjvvJrmraPnoa4K6Kej5p6Q77ya6K6h566X5py655eF5q+S5Y+v5Lul6YCa6L+H5aSa56eN6YCU5b6E5Lyg5pKt77yM5YyF5ous572R57uc44CB56e75Yqo5a2Y5YKo6K6+5aSH44CB55S15a2Q6YKu5Lu2562J44CCYAogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZpc2libGUodmFsKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHZhbAogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5yZXNldEltcG9ydCgpCiAgICAgIH0KICAgIH0sCiAgICBkaWFsb2dWaXNpYmxlKHZhbCkgewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIHZhbCkKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOiHquWumuS5ieS4iuS8oOaWueazlQogICAgY3VzdG9tVXBsb2FkKG9wdGlvbikgewogICAgICBjb25zdCBmaWxlID0gb3B0aW9uLmZpbGUKICAgICAgdGhpcy51cGxvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwCgogICAgICB1cGxvYWRBbmRQYXJzZShmaWxlLCB0aGlzLmJhbmtJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy51cGxvYWRpbmcgPSBmYWxzZQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMudXBsb2FkZWRGaWxlID0gewogICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWUsCiAgICAgICAgICAgIHJlc3BvbnNlOiByZXNwb25zZQogICAgICAgICAgfQogICAgICAgICAgdGhpcy5lZGl0Q29udGVudCA9IHJlc3BvbnNlLmRhdGEuZWRpdGFibGVDb250ZW50IHx8ICcnCiAgICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcmVzcG9uc2UuZGF0YS5lcnJvcnMgfHwgW10KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5paH5qGj5LiK5Lyg5oiQ5YqfJykKICAgICAgICAgIG9wdGlvbi5vblN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfmlofmoaPkuIrkvKDlpLHotKUnKQogICAgICAgICAgb3B0aW9uLm9uRXJyb3IobmV3IEVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5LiK5Lyg5aSx6LSlJyksIGZpbGUpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy51cGxvYWRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pScpCiAgICAgICAgb3B0aW9uLm9uRXJyb3IoZXJyb3IsIGZpbGUpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOaWh+S7tuS4iuS8oOWJjemqjOivgQogICAgYmVmb3JlRmlsZVVwbG9hZChmaWxlKSB7CiAgICAgIGNvbnN0IGlzRG9jeCA9IGZpbGUubmFtZS50b0xvd2VyQ2FzZSgpLmVuZHNXaXRoKCcuZG9jeCcpCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwCgogICAgICBpZiAoIWlzRG9jeCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oC5kb2N45qC85byP55qEV29yZOaWh+ahoyEnKQogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICAgIGlmICghaXNMdDEwTSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzEwTUIhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQoKICAgICAgdGhpcy51cGxvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwCiAgICAgIHJldHVybiB0cnVlCiAgICB9LAoKICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKn++8iOeUsWN1c3RvbVVwbG9hZOiwg+eUqO+8iQogICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUpIHsKICAgICAgLy8g6L+Z5Liq5pa55rOV546w5Zyo55SxY3VzdG9tVXBsb2Fk5aSE55CG77yM5L+d55WZ5Lul6Ziy6ZyA6KaBCiAgICB9LAoKICAgIC8vIOaWh+S7tuS4iuS8oOWksei0pe+8iOeUsWN1c3RvbVVwbG9hZOiwg+eUqO+8iQogICAgaGFuZGxlRmlsZUVycm9yKGVyciwgZmlsZSkgewogICAgICAvLyDov5nkuKrmlrnms5XnjrDlnKjnlLFjdXN0b21VcGxvYWTlpITnkIbvvIzkv53nlZnku6XpmLLpnIDopoEKICAgIH0sCgogICAgLy8g57yW6L6R5Zmo5YaF5a655Y+Y5YyWCiAgICBvbkNvbnRlbnRDaGFuZ2UoKSB7CiAgICAgIC8vIOmYsuaKluWkhOeQhu+8jOmBv+WFjemikee5geivt+axggogICAgICBjbGVhclRpbWVvdXQodGhpcy5wcmV2aWV3VGltZXIpCiAgICAgIHRoaXMucHJldmlld1RpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5hdXRvUHJldmlldygpCiAgICAgIH0sIDEwMDApCiAgICB9LAoKICAgIC8vIOiHquWKqOmihOiniAogICAgYXV0b1ByZXZpZXcoKSB7CiAgICAgIGlmICghdGhpcy5lZGl0Q29udGVudCB8fCB0aGlzLmVkaXRDb250ZW50LnRyaW0oKSA9PT0gJycpIHsKICAgICAgICB0aGlzLnByZXZpZXdIdG1sID0gJycKICAgICAgICB0aGlzLnByZXZpZXdRdWVzdGlvbkNvdW50ID0gMAogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMucHJldmlld0NvbnRlbnQoKQogICAgfSwKCiAgICAvLyDkv53lrZjlhoXlrrkKICAgIHNhdmVDb250ZW50KCkgewogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQsCiAgICAgICAgY29udGVudDogdGhpcy5lZGl0Q29udGVudAogICAgICB9CiAgICAgIHNhdmVFZGl0ZWRDb250ZW50KGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5YaF5a655L+d5a2Y5oiQ5YqfJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+S/neWtmOWksei0pScpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5YaF5a655aSx6LSlJywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5aSx6LSlJykKICAgICAgfSkKICAgIH0sCgogICAgLy8g6aKE6KeI5YaF5a65CiAgICBwcmV2aWV3Q29udGVudCgpIHsKICAgICAgaWYgKCF0aGlzLmVkaXRDb250ZW50IHx8IHRoaXMuZWRpdENvbnRlbnQudHJpbSgpID09PSAnJykgewogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLnByZXZpZXdMb2FkaW5nID0gdHJ1ZQogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGNvbnRlbnQ6IHRoaXMuZWRpdENvbnRlbnQgIC8vIOeOsOWcqOebtOaOpeS8oOmAkuaWh+acrOWGheWuue+8jOS4jemcgOimgUhUTUzovazmjaIKICAgICAgfQogICAgICAKICAgICAgcHJldmlld0NvbnRlbnQoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wcmV2aWV3TG9hZGluZyA9IGZhbHNlCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5wcmV2aWV3SHRtbCA9IHJlc3BvbnNlLmRhdGEucHJldmlld0h0bWwgfHwgJycKICAgICAgICAgIHRoaXMucHJldmlld1F1ZXN0aW9uQ291bnQgPSByZXNwb25zZS5kYXRhLnF1ZXN0aW9uQ291bnQgfHwgMAogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmRhdGEuZXJyb3JzIHx8IFtdCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfpooTop4jlpLHotKUnKQogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIHRoaXMucHJldmlld0xvYWRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mihOiniOWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+mihOiniOWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOehruiupOWvvOWFpQogICAgY29uZmlybUltcG9ydCgpIHsKICAgICAgdGhpcy5pbXBvcnRpbmcgPSB0cnVlCiAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZCwKICAgICAgICBjb250ZW50OiB0aGlzLmVkaXRDb250ZW50LAogICAgICAgIGFsbG93RHVwbGljYXRlOiB0aGlzLmltcG9ydE9wdGlvbnMuYWxsb3dEdXBsaWNhdGUKICAgICAgfQogICAgICAKICAgICAgaW1wb3J0RnJvbUVkaXRvcihkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmltcG9ydGluZyA9IGZhbHNlCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5pbXBvcnRSZXN1bHQgPSByZXNwb25zZS5kYXRhCiAgICAgICAgICB0aGlzLm5leHRTdGVwKCkKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6aKY55uu5a+85YWl5oiQ5YqfJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+WvvOWFpeWksei0pScpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy5pbXBvcnRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WvvOWFpeWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWFpeWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOS4i+S4gOatpQogICAgbmV4dFN0ZXAoKSB7CiAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwIDwgMykgewogICAgICAgIHRoaXMuY3VycmVudFN0ZXArKwogICAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwID09PSAyKSB7CiAgICAgICAgICAvLyDov5vlhaXpooTop4jnoa7orqTmraXpqqTml7bvvIzoh6rliqjpooTop4gKICAgICAgICAgIHRoaXMucHJldmlld0NvbnRlbnQoKQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvLyDkuIrkuIDmraUKICAgIHByZXZTdGVwKCkgewogICAgICBpZiAodGhpcy5jdXJyZW50U3RlcCA+IDApIHsKICAgICAgICB0aGlzLmN1cnJlbnRTdGVwLS0KICAgICAgfQogICAgfSwKCiAgICAvLyDlrozmiJDlr7zlhaUKICAgIGhhbmRsZUNvbXBsZXRlKCkgewogICAgICB0aGlzLiRlbWl0KCdzdWNjZXNzJykKICAgICAgdGhpcy5oYW5kbGVDbG9zZSgpCiAgICB9LAoKICAgIC8vIOmHjee9ruWvvOWFpQogICAgcmVzZXRJbXBvcnQoKSB7CiAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAwCiAgICAgIHRoaXMudXBsb2FkZWRGaWxlID0gbnVsbAogICAgICB0aGlzLnVwbG9hZGluZyA9IGZhbHNlCiAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwCiAgICAgIHRoaXMuZWRpdENvbnRlbnQgPSAnJwogICAgICB0aGlzLnByZXZpZXdIdG1sID0gJycKICAgICAgdGhpcy5wcmV2aWV3UXVlc3Rpb25Db3VudCA9IDAKICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCiAgICAgIHRoaXMuaW1wb3J0aW5nID0gZmFsc2UKICAgICAgdGhpcy5pbXBvcnRPcHRpb25zID0gewogICAgICAgIGFsbG93RHVwbGljYXRlOiBmYWxzZQogICAgICB9CiAgICAgIHRoaXMuaW1wb3J0UmVzdWx0ID0gewogICAgICAgIHN1Y2Nlc3NDb3VudDogMCwKICAgICAgICBmYWlsQ291bnQ6IDAsCiAgICAgICAgZXJyb3JzOiBbXQogICAgICB9CiAgICB9LAoKICAgIC8vIOWFs+mXreWvueivneahhgogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICB9LAoKICAgIC8vIOWKoOi9vea8lOekuuWGheWuuQogICAgbG9hZERlbW9Db250ZW50KCkgewogICAgICBjb25zdCBkZW1vQ29udGVudCA9IGAx44CBW+WNlemAiemimF3orqHnrpfmnLrnmoRDUFXkuLvopoHlip/og73mmK/ku4DkuYjvvJ8KQeOAgeWtmOWCqOaVsOaNrgpC44CB5aSE55CG5pWw5o2u5ZKM5o6n5Yi256iL5bqP5omn6KGMCkPjgIHovpPlhaXmlbDmja4KROOAgei+k+WHuuaVsOaNrgrnrZTmoYjvvJpCCuino+aekO+8mkNQVe+8iOS4reWkruWkhOeQhuWZqO+8ieaYr+iuoeeul+acuueahOaguOW/g+mDqOS7tu+8jOS4u+imgei0n+i0o+WkhOeQhuaVsOaNruWSjOaOp+WItueoi+W6j+eahOaJp+ihjOOAggoKMuOAgVvlpJrpgInpophd5Lul5LiL5ZOq5Lqb5piv6K6h566X5py655qE6L6T5YWl6K6+5aSH77yfCkHjgIHplK7nm5gKQuOAgem8oOaghwpD44CB5pi+56S65ZmoCkTjgIHmiavmj4/ku6oK562U5qGI77yaQSxCLEQK6Kej5p6Q77ya6ZSu55uY44CB6byg5qCH5ZKM5omr5o+P5Luq6YO95piv6L6T5YWl6K6+5aSH77yM5pi+56S65Zmo5piv6L6T5Ye66K6+5aSH44CCCgoz44CBW+WIpOaWremimF1DUFXnmoTkuLvpopHotorpq5jvvIzorqHnrpfmnLrnmoTmgKfog73lsLHkuIDlrprotorlpb3jgIIK562U5qGI77ya6ZSZ6K+vCuino+aekO+8mkNQVeaAp+iDveS4jeS7heWPluWGs+S6juS4u+mike+8jOi/mOS4juaetuaehOOAgee8k+WtmOOAgeaguOW/g+aVsOetieWkmuS4quWboOe0oOacieWFs+OAgmAKCiAgICAgIHRoaXMuZWRpdENvbnRlbnQgPSBkZW1vQ29udGVudAogICAgICB0aGlzLnVwbG9hZGVkRmlsZSA9IHsKICAgICAgICBuYW1lOiAn5ryU56S65YaF5a65LmRvY3gnLAogICAgICAgIHJlc3BvbnNlOiB7IGNvZGU6IDIwMCB9CiAgICAgIH0KICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5ryU56S65YaF5a655Yqg6L295oiQ5Yqf77yM5Y+v5Lul55u05o6l6L+b5YWl5LiL5LiA5q2lJykKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["RichTextImport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoRA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RichTextImport.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"题库文档导入\"\n    :visible.sync=\"dialogVisible\"\n    width=\"95%\"\n    :before-close=\"handleClose\"\n    append-to-body\n    class=\"rich-text-import-dialog\"\n  >\n    <div class=\"import-container\">\n      <!-- 步骤指示器 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"上传文档\"></el-step>\n        <el-step title=\"编辑内容\"></el-step>\n        <el-step title=\"预览确认\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 文档上传 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <div class=\"upload-options\">\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n              <el-tab-pane label=\"上传文档\" name=\"upload\">\n                <div class=\"upload-content\">\n                  <h3>上传Word文档</h3>\n                  <p>请上传包含题目的Word文档(.docx格式)，系统将自动解析文档内容</p>\n\n                  <div class=\"demo-section\">\n                    <el-button size=\"small\" type=\"text\" @click=\"loadDemoContent\">\n                      <i class=\"el-icon-magic-stick\"></i> 加载演示内容\n                    </el-button>\n                    <span class=\"demo-tip\">或者点击加载演示内容快速体验功能</span>\n                  </div>\n\n                  <el-upload\n                    ref=\"fileUpload\"\n                    :http-request=\"customUpload\"\n                    :on-success=\"handleFileSuccess\"\n                    :on-error=\"handleFileError\"\n                    :before-upload=\"beforeFileUpload\"\n                    :show-file-list=\"false\"\n                    accept=\".docx\"\n                    drag\n                  >\n                    <div class=\"upload-area\">\n                      <i class=\"el-icon-upload\"></i>\n                      <div class=\"upload-text\">\n                        <p>将Word文档拖到此处，或<em>点击上传</em></p>\n                        <p class=\"upload-tip\">支持 .docx 格式文件，文件大小不超过10MB</p>\n                      </div>\n                    </div>\n                  </el-upload>\n\n                  <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n                    <el-alert\n                      :title=\"`已上传文件：${uploadedFile.name}`\"\n                      type=\"success\"\n                      :closable=\"false\"\n                      show-icon\n                    />\n                  </div>\n\n                  <div v-if=\"uploading\" class=\"uploading-status\">\n                    <el-progress :percentage=\"50\" :show-text=\"false\"></el-progress>\n                    <p>正在上传并解析文档...</p>\n                  </div>\n                </div>\n              </el-tab-pane>\n\n              <el-tab-pane label=\"输入规范与范例\" name=\"rules\">\n                <div class=\"rules-content\">\n                  <h3>题目输入格式规范</h3>\n\n                  <div class=\"format-rules\">\n                    <div class=\"rule-section\">\n                      <h4>基本格式要求</h4>\n                      <ul>\n                        <li><strong>题目编号：</strong>使用\"数字、\"格式，如：1、2、3、</li>\n                        <li><strong>题型标识：</strong>[单选题]、[多选题]、[判断题]</li>\n                        <li><strong>选项格式：</strong>A、B、C、D、（大写字母+顿号）</li>\n                        <li><strong>答案格式：</strong>答案：A 或 答案：A,B,C</li>\n                        <li><strong>解析格式：</strong>解析：解析内容（可选）</li>\n                      </ul>\n                    </div>\n\n                    <div class=\"rule-section\">\n                      <h4>标准范例</h4>\n                      <div class=\"example-container\">\n                        <div class=\"example-header\">\n                          <span>完整题目示例</span>\n                          <el-button size=\"mini\" type=\"primary\" @click=\"copyExampleToEditor\">\n                            <i class=\"el-icon-copy-document\"></i> 复制到编辑器\n                          </el-button>\n                        </div>\n                        <pre class=\"example-content\">{{ exampleContent }}</pre>\n                      </div>\n                    </div>\n\n                    <div class=\"rule-section\">\n                      <h4>注意事项</h4>\n                      <el-alert\n                        title=\"格式要求\"\n                        type=\"warning\"\n                        :closable=\"false\"\n                        show-icon\n                      >\n                        <ul>\n                          <li>严格按照格式要求编写，格式错误会导致解析失败</li>\n                          <li>题目编号必须连续，不能跳号</li>\n                          <li>选项字母必须大写，使用中文顿号</li>\n                          <li>多选题答案用逗号分隔，如：A,B,C</li>\n                          <li>判断题答案只能是\"正确\"或\"错误\"</li>\n                        </ul>\n                      </el-alert>\n                    </div>\n                  </div>\n                </div>\n              </el-tab-pane>\n            </el-tabs>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"handleClose\">取消</el-button>\n          <el-button type=\"primary\" :disabled=\"!uploadedFile\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 富文本编辑 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"editor-container\">\n          <div class=\"editor-header\">\n            <h3>编辑文档内容</h3>\n            <div class=\"editor-actions\">\n              <el-button size=\"small\" @click=\"saveContent\">保存内容</el-button>\n              <el-button size=\"small\" type=\"primary\" @click=\"previewContent\">实时预览</el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-preview-layout\">\n            <!-- 左侧富文本编辑器 -->\n            <div class=\"editor-panel\">\n              <div class=\"panel-header\">\n                <h4>编辑区域</h4>\n                <span class=\"panel-tip\">您可以在此编辑题目内容</span>\n              </div>\n              <div class=\"editor-wrapper\">\n                <el-input\n                  v-model=\"editContent\"\n                  type=\"textarea\"\n                  :rows=\"15\"\n                  placeholder=\"请编辑题目内容...\"\n                  @input=\"onContentChange\"\n                  class=\"content-editor\"\n                />\n              </div>\n            </div>\n\n            <!-- 右侧预览区域 -->\n            <div class=\"preview-panel\">\n              <div class=\"panel-header\">\n                <h4>实时预览</h4>\n                <span class=\"panel-tip\">题目解析结果：{{ previewQuestionCount }}道题目</span>\n              </div>\n              <div class=\"preview-wrapper\">\n                <div v-if=\"previewLoading\" class=\"preview-loading\">\n                  <el-loading text=\"正在解析预览...\"></el-loading>\n                </div>\n                <div v-else-if=\"previewHtml\" class=\"preview-content\" v-html=\"previewHtml\"></div>\n                <div v-else class=\"preview-empty\">\n                  <el-empty description=\"暂无预览内容，请编辑左侧内容\"></el-empty>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :disabled=\"!editContent\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 预览确认 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"confirm-section\">\n          <h3>确认导入</h3>\n          <div class=\"import-summary\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析题目数量\" :value=\"previewQuestionCount\" suffix=\"道\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析错误\" :value=\"parseErrors.length\" suffix=\"个\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"目标题库\" :value=\"bankName\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <h4>解析错误信息</h4>\n            <el-alert\n              v-for=\"(error, index) in parseErrors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"warning\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n\n          <div class=\"import-options\">\n            <h4>导入选项</h4>\n            <el-checkbox v-model=\"importOptions.allowDuplicate\">允许导入重复题目</el-checkbox>\n          </div>\n\n          <div class=\"final-preview\">\n            <h4>最终预览</h4>\n            <div class=\"preview-content\" v-html=\"previewHtml\"></div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :loading=\"importing\" @click=\"confirmImport\">确认导入</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 64px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          \n          <div class=\"import-result\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"成功导入\" :value=\"importResult.successCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"导入失败\" :value=\"importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"总计处理\" :value=\"importResult.successCount + importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"import-errors\">\n            <h4>导入错误详情</h4>\n            <el-alert\n              v-for=\"(error, index) in importResult.errors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"error\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { uploadAndParse, saveEditedContent, previewContent, importFromEditor } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"RichTextImport\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    bankName: {\n      type: String,\n      default: '题库'\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n\n      // 标签页相关\n      activeTab: 'upload',\n\n      // 文件上传相关\n      uploadedFile: null,\n      uploading: false,\n      uploadProgress: 0,\n      \n      // 编辑器相关\n      editContent: '',\n      \n      // 预览相关\n      previewHtml: '',\n      previewQuestionCount: 0,\n      previewLoading: false,\n      parseErrors: [],\n      \n      // 导入相关\n      importing: false,\n      importOptions: {\n        allowDuplicate: false\n      },\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      },\n\n      // 示例内容\n      exampleContent: `1、[单选题]计算机的CPU主要功能是什么？\nA、存储数据\nB、处理数据和控制程序执行\nC、输入数据\nD、输出数据\n答案：B\n解析：CPU（中央处理器）是计算机的核心部件，主要负责处理数据和控制程序的执行。\n\n2、[多选题]以下哪些是计算机的输入设备？\nA、键盘\nB、鼠标\nC、显示器\nD、扫描仪\nE、打印机\n答案：A,B,D\n解析：键盘、鼠标和扫描仪都是输入设备，显示器和打印机是输出设备。\n\n3、[判断题]CPU的主频越高，计算机的性能就一定越好。\n答案：错误\n解析：CPU性能不仅取决于主频，还与架构、缓存、核心数等多个因素有关。\n\n4、[单选题]以下哪个不是操作系统？\nA、Windows\nB、Linux\nC、Office\nD、macOS\n答案：C\n解析：Office是办公软件套件，不是操作系统。Windows、Linux和macOS都是操作系统。\n\n5、[判断题]计算机病毒可以通过网络传播。\n答案：正确\n解析：计算机病毒可以通过多种途径传播，包括网络、移动存储设备、电子邮件等。`\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  methods: {\n    // 自定义上传方法\n    customUpload(option) {\n      const file = option.file\n      this.uploading = true\n      this.uploadProgress = 0\n\n      uploadAndParse(file, this.bankId).then(response => {\n        this.uploading = false\n        if (response.code === 200) {\n          this.uploadedFile = {\n            name: file.name,\n            response: response\n          }\n          this.editContent = response.data.editableContent || ''\n          this.parseErrors = response.data.errors || []\n          this.$message.success('文档上传成功')\n          option.onSuccess(response, file)\n        } else {\n          this.$message.error(response.msg || '文档上传失败')\n          option.onError(new Error(response.msg || '上传失败'), file)\n        }\n      }).catch(error => {\n        this.uploading = false\n        console.error('文件上传失败', error)\n        this.$message.error('文件上传失败')\n        option.onError(error, file)\n      })\n    },\n\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isDocx = file.name.toLowerCase().endsWith('.docx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isDocx) {\n        this.$message.error('只能上传.docx格式的Word文档!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n\n      this.uploading = true\n      this.uploadProgress = 0\n      return true\n    },\n\n    // 文件上传成功（由customUpload调用）\n    handleFileSuccess(response, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 文件上传失败（由customUpload调用）\n    handleFileError(err, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 编辑器内容变化\n    onContentChange() {\n      // 防抖处理，避免频繁请求\n      clearTimeout(this.previewTimer)\n      this.previewTimer = setTimeout(() => {\n        this.autoPreview()\n      }, 1000)\n    },\n\n    // 自动预览\n    autoPreview() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        this.previewHtml = ''\n        this.previewQuestionCount = 0\n        return\n      }\n      this.previewContent()\n    },\n\n    // 保存内容\n    saveContent() {\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent\n      }\n      saveEditedContent(data).then(response => {\n        if (response.code === 200) {\n          this.$message.success('内容保存成功')\n        } else {\n          this.$message.error(response.msg || '保存失败')\n        }\n      }).catch(error => {\n        console.error('保存内容失败', error)\n        this.$message.error('保存失败')\n      })\n    },\n\n    // 预览内容\n    previewContent() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        return\n      }\n\n      this.previewLoading = true\n      const data = {\n        content: this.editContent  // 现在直接传递文本内容，不需要HTML转换\n      }\n      \n      previewContent(data).then(response => {\n        this.previewLoading = false\n        if (response.code === 200) {\n          this.previewHtml = response.data.previewHtml || ''\n          this.previewQuestionCount = response.data.questionCount || 0\n          this.parseErrors = response.data.errors || []\n        } else {\n          this.$message.error(response.msg || '预览失败')\n        }\n      }).catch(error => {\n        this.previewLoading = false\n        console.error('预览失败', error)\n        this.$message.error('预览失败')\n      })\n    },\n\n    // 确认导入\n    confirmImport() {\n      this.importing = true\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent,\n        allowDuplicate: this.importOptions.allowDuplicate\n      }\n      \n      importFromEditor(data).then(response => {\n        this.importing = false\n        if (response.code === 200) {\n          this.importResult = response.data\n          this.nextStep()\n          this.$message.success('题目导入成功')\n        } else {\n          this.$message.error(response.msg || '导入失败')\n        }\n      }).catch(error => {\n        this.importing = false\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      })\n    },\n\n    // 下一步\n    nextStep() {\n      if (this.currentStep < 3) {\n        this.currentStep++\n        if (this.currentStep === 2) {\n          // 进入预览确认步骤时，自动预览\n          this.previewContent()\n        }\n      }\n    },\n\n    // 上一步\n    prevStep() {\n      if (this.currentStep > 0) {\n        this.currentStep--\n      }\n    },\n\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.uploadedFile = null\n      this.uploading = false\n      this.uploadProgress = 0\n      this.editContent = ''\n      this.previewHtml = ''\n      this.previewQuestionCount = 0\n      this.parseErrors = []\n      this.importing = false\n      this.importOptions = {\n        allowDuplicate: false\n      }\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    },\n\n    // 加载演示内容\n    loadDemoContent() {\n      const demoContent = `1、[单选题]计算机的CPU主要功能是什么？\nA、存储数据\nB、处理数据和控制程序执行\nC、输入数据\nD、输出数据\n答案：B\n解析：CPU（中央处理器）是计算机的核心部件，主要负责处理数据和控制程序的执行。\n\n2、[多选题]以下哪些是计算机的输入设备？\nA、键盘\nB、鼠标\nC、显示器\nD、扫描仪\n答案：A,B,D\n解析：键盘、鼠标和扫描仪都是输入设备，显示器是输出设备。\n\n3、[判断题]CPU的主频越高，计算机的性能就一定越好。\n答案：错误\n解析：CPU性能不仅取决于主频，还与架构、缓存、核心数等多个因素有关。`\n\n      this.editContent = demoContent\n      this.uploadedFile = {\n        name: '演示内容.docx',\n        response: { code: 200 }\n      }\n      this.parseErrors = []\n      this.$message.success('演示内容加载成功，可以直接进入下一步')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.rich-text-import-dialog {\n  .el-dialog {\n    margin-top: 5vh !important;\n  }\n\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n\n.import-container {\n  min-height: 600px;\n}\n\n.step-content {\n  min-height: 500px;\n  display: flex;\n  flex-direction: column;\n}\n\n.step-actions {\n  margin-top: auto;\n  padding-top: 20px;\n  border-top: 1px solid #e9ecef;\n  text-align: right;\n}\n\n/* 文件上传样式 */\n.upload-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.upload-section p {\n  margin-bottom: 30px;\n  color: #666;\n  text-align: center;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 10px 0;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n.uploaded-file {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.uploading-status {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n  text-align: center;\n}\n\n.demo-section {\n  margin: 20px 0;\n  text-align: center;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border: 1px dashed #d9ecff;\n}\n\n.demo-tip {\n  margin-left: 10px;\n  font-size: 12px;\n  color: #666;\n}\n\n/* 编辑器样式 */\n.editor-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.editor-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.editor-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.editor-preview-layout {\n  flex: 1;\n  display: flex;\n  gap: 20px;\n  min-height: 400px;\n}\n\n.editor-panel,\n.preview-panel {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.panel-header {\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 14px;\n  color: #333;\n}\n\n.panel-tip {\n  font-size: 12px;\n  color: #666;\n}\n\n.editor-wrapper,\n.preview-wrapper {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.content-editor {\n  flex: 1;\n  min-height: 350px;\n}\n\n.preview-wrapper {\n  position: relative;\n}\n\n.preview-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n}\n\n.preview-content {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n  max-height: 350px;\n}\n\n.preview-empty {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 确认导入样式 */\n.confirm-section {\n  flex: 1;\n}\n\n.confirm-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.import-summary {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.error-section {\n  margin-bottom: 30px;\n}\n\n.error-section h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n.import-options {\n  margin-bottom: 30px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n}\n\n.import-options h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.final-preview {\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.final-preview h4 {\n  margin: 0;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  color: #333;\n}\n\n.final-preview .preview-content {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n/* 完成页面样式 */\n.result-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-section h3 {\n  margin-bottom: 30px;\n  color: #333;\n}\n\n.import-result {\n  margin-bottom: 30px;\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors {\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n/* 预览内容样式 */\n.preview-content .questions-preview {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.preview-content .question-item {\n  margin-bottom: 20px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.preview-content .question-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n  font-weight: 500;\n}\n\n.preview-content .question-number {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.preview-content .question-type {\n  color: #409eff;\n  font-weight: 500;\n}\n\n.preview-content .question-difficulty {\n  color: #666;\n  font-size: 12px;\n}\n\n.preview-content .question-content {\n  margin-bottom: 15px;\n  line-height: 1.6;\n  color: #333;\n}\n\n.preview-content .question-options {\n  margin-bottom: 15px;\n}\n\n.preview-content .option-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  margin-bottom: 8px;\n  padding: 8px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.preview-content .option-key {\n  font-weight: 500;\n  color: #409eff;\n  min-width: 20px;\n}\n\n.preview-content .option-content {\n  flex: 1;\n  line-height: 1.5;\n}\n\n.preview-content .question-answer {\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background: #e8f5e8;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.preview-content .question-analysis {\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .editor-preview-layout {\n    flex-direction: column;\n  }\n\n  .editor-panel,\n  .preview-panel {\n    min-height: 300px;\n  }\n}\n</style>\n"]}]}