{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJwppbXBvcnQgeyB1cGxvYWRBbmRQYXJzZSwgc2F2ZUVkaXRlZENvbnRlbnQsIHByZXZpZXdDb250ZW50LCBpbXBvcnRGcm9tRWRpdG9yIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uQmFuaycKaW1wb3J0IHsgcXVpbGxFZGl0b3IgfSBmcm9tICd2dWUtcXVpbGwtZWRpdG9yJwppbXBvcnQgJ3F1aWxsL2Rpc3QvcXVpbGwuY29yZS5jc3MnCmltcG9ydCAncXVpbGwvZGlzdC9xdWlsbC5zbm93LmNzcycKaW1wb3J0ICdxdWlsbC9kaXN0L3F1aWxsLmJ1YmJsZS5jc3MnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlJpY2hUZXh0SW1wb3J0IiwKICBjb21wb25lbnRzOiB7CiAgICBxdWlsbEVkaXRvcgogIH0sCiAgcHJvcHM6IHsKICAgIHZpc2libGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICBiYW5rSWQ6IHsKICAgICAgdHlwZTogW1N0cmluZywgTnVtYmVyXSwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBiYW5rTmFtZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICfpopjlupMnCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRTdGVwOiAwLAogICAgICAKICAgICAgLy8g5paH5Lu25LiK5Lyg55u45YWzCiAgICAgIHVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvYml6L3F1ZXN0aW9uQmFuay91cGxvYWRBbmRQYXJzZScsCiAgICAgIHVwbG9hZEhlYWRlcnM6IHsKICAgICAgICBBdXRob3JpemF0aW9uOiAnQmVhcmVyICcgKyBnZXRUb2tlbigpCiAgICAgIH0sCiAgICAgIHVwbG9hZGVkRmlsZTogbnVsbCwKICAgICAgdXBsb2FkaW5nOiBmYWxzZSwKICAgICAgdXBsb2FkUHJvZ3Jlc3M6IDAsCiAgICAgIAogICAgICAvLyDnvJbovpHlmajnm7jlhbMKICAgICAgZWRpdENvbnRlbnQ6ICcnLAogICAgICBlZGl0b3JPcHRpb25zOiB7CiAgICAgICAgdGhlbWU6ICdzbm93JywKICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+e8lui+kemimOebruWGheWuuS4uLicsCiAgICAgICAgbW9kdWxlczogewogICAgICAgICAgdG9vbGJhcjogWwogICAgICAgICAgICBbJ2JvbGQnLCAnaXRhbGljJywgJ3VuZGVybGluZScsICdzdHJpa2UnXSwKICAgICAgICAgICAgWydibG9ja3F1b3RlJywgJ2NvZGUtYmxvY2snXSwKICAgICAgICAgICAgW3sgJ2hlYWRlcic6IDEgfSwgeyAnaGVhZGVyJzogMiB9XSwKICAgICAgICAgICAgW3sgJ2xpc3QnOiAnb3JkZXJlZCd9LCB7ICdsaXN0JzogJ2J1bGxldCcgfV0sCiAgICAgICAgICAgIFt7ICdzY3JpcHQnOiAnc3ViJ30sIHsgJ3NjcmlwdCc6ICdzdXBlcicgfV0sCiAgICAgICAgICAgIFt7ICdpbmRlbnQnOiAnLTEnfSwgeyAnaW5kZW50JzogJysxJyB9XSwKICAgICAgICAgICAgW3sgJ2RpcmVjdGlvbic6ICdydGwnIH1dLAogICAgICAgICAgICBbeyAnc2l6ZSc6IFsnc21hbGwnLCBmYWxzZSwgJ2xhcmdlJywgJ2h1Z2UnXSB9XSwKICAgICAgICAgICAgW3sgJ2hlYWRlcic6IFsxLCAyLCAzLCA0LCA1LCA2LCBmYWxzZV0gfV0sCiAgICAgICAgICAgIFt7ICdjb2xvcic6IFtdIH0sIHsgJ2JhY2tncm91bmQnOiBbXSB9XSwKICAgICAgICAgICAgW3sgJ2ZvbnQnOiBbXSB9XSwKICAgICAgICAgICAgW3sgJ2FsaWduJzogW10gfV0sCiAgICAgICAgICAgIFsnY2xlYW4nXSwKICAgICAgICAgICAgWydsaW5rJywgJ2ltYWdlJ10KICAgICAgICAgIF0KICAgICAgICB9CiAgICAgIH0sCiAgICAgIAogICAgICAvLyDpooTop4jnm7jlhbMKICAgICAgcHJldmlld0h0bWw6ICcnLAogICAgICBwcmV2aWV3UXVlc3Rpb25Db3VudDogMCwKICAgICAgcHJldmlld0xvYWRpbmc6IGZhbHNlLAogICAgICBwYXJzZUVycm9yczogW10sCiAgICAgIAogICAgICAvLyDlr7zlhaXnm7jlhbMKICAgICAgaW1wb3J0aW5nOiBmYWxzZSwKICAgICAgaW1wb3J0T3B0aW9uczogewogICAgICAgIGFsbG93RHVwbGljYXRlOiBmYWxzZQogICAgICB9LAogICAgICBpbXBvcnRSZXN1bHQ6IHsKICAgICAgICBzdWNjZXNzQ291bnQ6IDAsCiAgICAgICAgZmFpbENvdW50OiAwLAogICAgICAgIGVycm9yczogW10KICAgICAgfQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZpc2libGUodmFsKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHZhbAogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5yZXNldEltcG9ydCgpCiAgICAgIH0KICAgIH0sCiAgICBkaWFsb2dWaXNpYmxlKHZhbCkgewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIHZhbCkKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOaWh+S7tuS4iuS8oOWJjemqjOivgQogICAgYmVmb3JlRmlsZVVwbG9hZChmaWxlKSB7CiAgICAgIGNvbnN0IGlzRG9jeCA9IGZpbGUubmFtZS50b0xvd2VyQ2FzZSgpLmVuZHNXaXRoKCcuZG9jeCcpCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwCgogICAgICBpZiAoIWlzRG9jeCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oC5kb2N45qC85byP55qEV29yZOaWh+ahoyEnKQogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICAgIGlmICghaXNMdDEwTSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzEwTUIhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQoKICAgICAgdGhpcy51cGxvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwCiAgICAgIHJldHVybiB0cnVlCiAgICB9LAoKICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKnwogICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUpIHsKICAgICAgdGhpcy51cGxvYWRpbmcgPSBmYWxzZQogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgdGhpcy51cGxvYWRlZEZpbGUgPSB7CiAgICAgICAgICBuYW1lOiBmaWxlLm5hbWUsCiAgICAgICAgICByZXNwb25zZTogcmVzcG9uc2UKICAgICAgICB9CiAgICAgICAgdGhpcy5lZGl0Q29udGVudCA9IHJlc3BvbnNlLmRhdGEuZWRpdGFibGVDb250ZW50IHx8ICcnCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmRhdGEuZXJyb3JzIHx8IFtdCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofmoaPkuIrkvKDmiJDlip8nKQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfmlofmoaPkuIrkvKDlpLHotKUnKQogICAgICB9CiAgICB9LAoKICAgIC8vIOaWh+S7tuS4iuS8oOWksei0pQogICAgaGFuZGxlRmlsZUVycm9yKGVyciwgZmlsZSkgewogICAgICB0aGlzLnVwbG9hZGluZyA9IGZhbHNlCiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pScsIGVycikKICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25LiK5Lyg5aSx6LSlJykKICAgIH0sCgogICAgLy8g57yW6L6R5Zmo5YaF5a655Y+Y5YyWCiAgICBvbkNvbnRlbnRDaGFuZ2UoKSB7CiAgICAgIC8vIOmYsuaKluWkhOeQhu+8jOmBv+WFjemikee5geivt+axggogICAgICBjbGVhclRpbWVvdXQodGhpcy5wcmV2aWV3VGltZXIpCiAgICAgIHRoaXMucHJldmlld1RpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5hdXRvUHJldmlldygpCiAgICAgIH0sIDEwMDApCiAgICB9LAoKICAgIC8vIOiHquWKqOmihOiniAogICAgYXV0b1ByZXZpZXcoKSB7CiAgICAgIGlmICghdGhpcy5lZGl0Q29udGVudCB8fCB0aGlzLmVkaXRDb250ZW50LnRyaW0oKSA9PT0gJycpIHsKICAgICAgICB0aGlzLnByZXZpZXdIdG1sID0gJycKICAgICAgICB0aGlzLnByZXZpZXdRdWVzdGlvbkNvdW50ID0gMAogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMucHJldmlld0NvbnRlbnQoKQogICAgfSwKCiAgICAvLyDkv53lrZjlhoXlrrkKICAgIHNhdmVDb250ZW50KCkgewogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQsCiAgICAgICAgY29udGVudDogdGhpcy5lZGl0Q29udGVudAogICAgICB9CiAgICAgIHNhdmVFZGl0ZWRDb250ZW50KGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5YaF5a655L+d5a2Y5oiQ5YqfJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+S/neWtmOWksei0pScpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5YaF5a655aSx6LSlJywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5aSx6LSlJykKICAgICAgfSkKICAgIH0sCgogICAgLy8g6aKE6KeI5YaF5a65CiAgICBwcmV2aWV3Q29udGVudCgpIHsKICAgICAgaWYgKCF0aGlzLmVkaXRDb250ZW50IHx8IHRoaXMuZWRpdENvbnRlbnQudHJpbSgpID09PSAnJykgewogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLnByZXZpZXdMb2FkaW5nID0gdHJ1ZQogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGNvbnRlbnQ6IHRoaXMuZWRpdENvbnRlbnQKICAgICAgfQogICAgICAKICAgICAgcHJldmlld0NvbnRlbnQoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wcmV2aWV3TG9hZGluZyA9IGZhbHNlCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5wcmV2aWV3SHRtbCA9IHJlc3BvbnNlLmRhdGEucHJldmlld0h0bWwgfHwgJycKICAgICAgICAgIHRoaXMucHJldmlld1F1ZXN0aW9uQ291bnQgPSByZXNwb25zZS5kYXRhLnF1ZXN0aW9uQ291bnQgfHwgMAogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmRhdGEuZXJyb3JzIHx8IFtdCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfpooTop4jlpLHotKUnKQogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIHRoaXMucHJldmlld0xvYWRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mihOiniOWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+mihOiniOWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOehruiupOWvvOWFpQogICAgY29uZmlybUltcG9ydCgpIHsKICAgICAgdGhpcy5pbXBvcnRpbmcgPSB0cnVlCiAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZCwKICAgICAgICBjb250ZW50OiB0aGlzLmVkaXRDb250ZW50LAogICAgICAgIGFsbG93RHVwbGljYXRlOiB0aGlzLmltcG9ydE9wdGlvbnMuYWxsb3dEdXBsaWNhdGUKICAgICAgfQogICAgICAKICAgICAgaW1wb3J0RnJvbUVkaXRvcihkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmltcG9ydGluZyA9IGZhbHNlCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5pbXBvcnRSZXN1bHQgPSByZXNwb25zZS5kYXRhCiAgICAgICAgICB0aGlzLm5leHRTdGVwKCkKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6aKY55uu5a+85YWl5oiQ5YqfJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+WvvOWFpeWksei0pScpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy5pbXBvcnRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WvvOWFpeWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWFpeWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOS4i+S4gOatpQogICAgbmV4dFN0ZXAoKSB7CiAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwIDwgMykgewogICAgICAgIHRoaXMuY3VycmVudFN0ZXArKwogICAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwID09PSAyKSB7CiAgICAgICAgICAvLyDov5vlhaXpooTop4jnoa7orqTmraXpqqTml7bvvIzoh6rliqjpooTop4gKICAgICAgICAgIHRoaXMucHJldmlld0NvbnRlbnQoKQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvLyDkuIrkuIDmraUKICAgIHByZXZTdGVwKCkgewogICAgICBpZiAodGhpcy5jdXJyZW50U3RlcCA+IDApIHsKICAgICAgICB0aGlzLmN1cnJlbnRTdGVwLS0KICAgICAgfQogICAgfSwKCiAgICAvLyDlrozmiJDlr7zlhaUKICAgIGhhbmRsZUNvbXBsZXRlKCkgewogICAgICB0aGlzLiRlbWl0KCdzdWNjZXNzJykKICAgICAgdGhpcy5oYW5kbGVDbG9zZSgpCiAgICB9LAoKICAgIC8vIOmHjee9ruWvvOWFpQogICAgcmVzZXRJbXBvcnQoKSB7CiAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAwCiAgICAgIHRoaXMudXBsb2FkZWRGaWxlID0gbnVsbAogICAgICB0aGlzLnVwbG9hZGluZyA9IGZhbHNlCiAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwCiAgICAgIHRoaXMuZWRpdENvbnRlbnQgPSAnJwogICAgICB0aGlzLnByZXZpZXdIdG1sID0gJycKICAgICAgdGhpcy5wcmV2aWV3UXVlc3Rpb25Db3VudCA9IDAKICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCiAgICAgIHRoaXMuaW1wb3J0aW5nID0gZmFsc2UKICAgICAgdGhpcy5pbXBvcnRPcHRpb25zID0gewogICAgICAgIGFsbG93RHVwbGljYXRlOiBmYWxzZQogICAgICB9CiAgICAgIHRoaXMuaW1wb3J0UmVzdWx0ID0gewogICAgICAgIHN1Y2Nlc3NDb3VudDogMCwKICAgICAgICBmYWlsQ291bnQ6IDAsCiAgICAgICAgZXJyb3JzOiBbXQogICAgICB9CiAgICB9LAoKICAgIC8vIOWFs+mXreWvueivneahhgogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["RichTextImport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkNA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "RichTextImport.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"题库文档导入\"\n    :visible.sync=\"dialogVisible\"\n    width=\"95%\"\n    :before-close=\"handleClose\"\n    append-to-body\n    class=\"rich-text-import-dialog\"\n  >\n    <div class=\"import-container\">\n      <!-- 步骤指示器 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"上传文档\"></el-step>\n        <el-step title=\"编辑内容\"></el-step>\n        <el-step title=\"预览确认\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 文档上传 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <h3>上传Word文档</h3>\n          <p>请上传包含题目的Word文档(.docx格式)，系统将自动解析文档内容</p>\n\n          <el-upload\n            ref=\"fileUpload\"\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :on-success=\"handleFileSuccess\"\n            :on-error=\"handleFileError\"\n            :before-upload=\"beforeFileUpload\"\n            :show-file-list=\"false\"\n            :accept=\".docx\"\n            drag\n          >\n            <div class=\"upload-area\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"upload-text\">\n                <p>将Word文档拖到此处，或<em>点击上传</em></p>\n                <p class=\"upload-tip\">支持 .docx 格式文件，文件大小不超过10MB</p>\n              </div>\n            </div>\n          </el-upload>\n\n          <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n            <el-alert\n              :title=\"`已上传文件：${uploadedFile.name}`\"\n              type=\"success\"\n              :closable=\"false\"\n              show-icon\n            />\n          </div>\n\n          <div v-if=\"uploading\" class=\"uploading-status\">\n            <el-progress :percentage=\"uploadProgress\" :show-text=\"false\"></el-progress>\n            <p>正在上传并解析文档...</p>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"handleClose\">取消</el-button>\n          <el-button type=\"primary\" :disabled=\"!uploadedFile\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 富文本编辑 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"editor-container\">\n          <div class=\"editor-header\">\n            <h3>编辑文档内容</h3>\n            <div class=\"editor-actions\">\n              <el-button size=\"small\" @click=\"saveContent\">保存内容</el-button>\n              <el-button size=\"small\" type=\"primary\" @click=\"previewContent\">实时预览</el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-preview-layout\">\n            <!-- 左侧富文本编辑器 -->\n            <div class=\"editor-panel\">\n              <div class=\"panel-header\">\n                <h4>编辑区域</h4>\n                <span class=\"panel-tip\">您可以在此编辑题目内容</span>\n              </div>\n              <div class=\"editor-wrapper\">\n                <quill-editor\n                  v-model=\"editContent\"\n                  :options=\"editorOptions\"\n                  @change=\"onContentChange\"\n                  class=\"content-editor\"\n                />\n              </div>\n            </div>\n\n            <!-- 右侧预览区域 -->\n            <div class=\"preview-panel\">\n              <div class=\"panel-header\">\n                <h4>实时预览</h4>\n                <span class=\"panel-tip\">题目解析结果：{{ previewQuestionCount }}道题目</span>\n              </div>\n              <div class=\"preview-wrapper\">\n                <div v-if=\"previewLoading\" class=\"preview-loading\">\n                  <el-loading text=\"正在解析预览...\"></el-loading>\n                </div>\n                <div v-else-if=\"previewHtml\" class=\"preview-content\" v-html=\"previewHtml\"></div>\n                <div v-else class=\"preview-empty\">\n                  <el-empty description=\"暂无预览内容，请编辑左侧内容\"></el-empty>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :disabled=\"!editContent\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 预览确认 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"confirm-section\">\n          <h3>确认导入</h3>\n          <div class=\"import-summary\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析题目数量\" :value=\"previewQuestionCount\" suffix=\"道\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析错误\" :value=\"parseErrors.length\" suffix=\"个\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"目标题库\" :value=\"bankName\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <h4>解析错误信息</h4>\n            <el-alert\n              v-for=\"(error, index) in parseErrors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"warning\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n\n          <div class=\"import-options\">\n            <h4>导入选项</h4>\n            <el-checkbox v-model=\"importOptions.allowDuplicate\">允许导入重复题目</el-checkbox>\n          </div>\n\n          <div class=\"final-preview\">\n            <h4>最终预览</h4>\n            <div class=\"preview-content\" v-html=\"previewHtml\"></div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :loading=\"importing\" @click=\"confirmImport\">确认导入</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 64px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          \n          <div class=\"import-result\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"成功导入\" :value=\"importResult.successCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"导入失败\" :value=\"importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"总计处理\" :value=\"importResult.successCount + importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"import-errors\">\n            <h4>导入错误详情</h4>\n            <el-alert\n              v-for=\"(error, index) in importResult.errors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"error\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { uploadAndParse, saveEditedContent, previewContent, importFromEditor } from '@/api/biz/questionBank'\nimport { quillEditor } from 'vue-quill-editor'\nimport 'quill/dist/quill.core.css'\nimport 'quill/dist/quill.snow.css'\nimport 'quill/dist/quill.bubble.css'\n\nexport default {\n  name: \"RichTextImport\",\n  components: {\n    quillEditor\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    bankName: {\n      type: String,\n      default: '题库'\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n      \n      // 文件上传相关\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadAndParse',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      },\n      uploadedFile: null,\n      uploading: false,\n      uploadProgress: 0,\n      \n      // 编辑器相关\n      editContent: '',\n      editorOptions: {\n        theme: 'snow',\n        placeholder: '请编辑题目内容...',\n        modules: {\n          toolbar: [\n            ['bold', 'italic', 'underline', 'strike'],\n            ['blockquote', 'code-block'],\n            [{ 'header': 1 }, { 'header': 2 }],\n            [{ 'list': 'ordered'}, { 'list': 'bullet' }],\n            [{ 'script': 'sub'}, { 'script': 'super' }],\n            [{ 'indent': '-1'}, { 'indent': '+1' }],\n            [{ 'direction': 'rtl' }],\n            [{ 'size': ['small', false, 'large', 'huge'] }],\n            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],\n            [{ 'color': [] }, { 'background': [] }],\n            [{ 'font': [] }],\n            [{ 'align': [] }],\n            ['clean'],\n            ['link', 'image']\n          ]\n        }\n      },\n      \n      // 预览相关\n      previewHtml: '',\n      previewQuestionCount: 0,\n      previewLoading: false,\n      parseErrors: [],\n      \n      // 导入相关\n      importing: false,\n      importOptions: {\n        allowDuplicate: false\n      },\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  methods: {\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isDocx = file.name.toLowerCase().endsWith('.docx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isDocx) {\n        this.$message.error('只能上传.docx格式的Word文档!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n\n      this.uploading = true\n      this.uploadProgress = 0\n      return true\n    },\n\n    // 文件上传成功\n    handleFileSuccess(response, file) {\n      this.uploading = false\n      if (response.code === 200) {\n        this.uploadedFile = {\n          name: file.name,\n          response: response\n        }\n        this.editContent = response.data.editableContent || ''\n        this.parseErrors = response.data.errors || []\n        this.$message.success('文档上传成功')\n      } else {\n        this.$message.error(response.msg || '文档上传失败')\n      }\n    },\n\n    // 文件上传失败\n    handleFileError(err, file) {\n      this.uploading = false\n      console.error('文件上传失败', err)\n      this.$message.error('文件上传失败')\n    },\n\n    // 编辑器内容变化\n    onContentChange() {\n      // 防抖处理，避免频繁请求\n      clearTimeout(this.previewTimer)\n      this.previewTimer = setTimeout(() => {\n        this.autoPreview()\n      }, 1000)\n    },\n\n    // 自动预览\n    autoPreview() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        this.previewHtml = ''\n        this.previewQuestionCount = 0\n        return\n      }\n      this.previewContent()\n    },\n\n    // 保存内容\n    saveContent() {\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent\n      }\n      saveEditedContent(data).then(response => {\n        if (response.code === 200) {\n          this.$message.success('内容保存成功')\n        } else {\n          this.$message.error(response.msg || '保存失败')\n        }\n      }).catch(error => {\n        console.error('保存内容失败', error)\n        this.$message.error('保存失败')\n      })\n    },\n\n    // 预览内容\n    previewContent() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        return\n      }\n\n      this.previewLoading = true\n      const data = {\n        content: this.editContent\n      }\n      \n      previewContent(data).then(response => {\n        this.previewLoading = false\n        if (response.code === 200) {\n          this.previewHtml = response.data.previewHtml || ''\n          this.previewQuestionCount = response.data.questionCount || 0\n          this.parseErrors = response.data.errors || []\n        } else {\n          this.$message.error(response.msg || '预览失败')\n        }\n      }).catch(error => {\n        this.previewLoading = false\n        console.error('预览失败', error)\n        this.$message.error('预览失败')\n      })\n    },\n\n    // 确认导入\n    confirmImport() {\n      this.importing = true\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent,\n        allowDuplicate: this.importOptions.allowDuplicate\n      }\n      \n      importFromEditor(data).then(response => {\n        this.importing = false\n        if (response.code === 200) {\n          this.importResult = response.data\n          this.nextStep()\n          this.$message.success('题目导入成功')\n        } else {\n          this.$message.error(response.msg || '导入失败')\n        }\n      }).catch(error => {\n        this.importing = false\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      })\n    },\n\n    // 下一步\n    nextStep() {\n      if (this.currentStep < 3) {\n        this.currentStep++\n        if (this.currentStep === 2) {\n          // 进入预览确认步骤时，自动预览\n          this.previewContent()\n        }\n      }\n    },\n\n    // 上一步\n    prevStep() {\n      if (this.currentStep > 0) {\n        this.currentStep--\n      }\n    },\n\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.uploadedFile = null\n      this.uploading = false\n      this.uploadProgress = 0\n      this.editContent = ''\n      this.previewHtml = ''\n      this.previewQuestionCount = 0\n      this.parseErrors = []\n      this.importing = false\n      this.importOptions = {\n        allowDuplicate: false\n      }\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    }\n  }\n}\n</script>\n"]}]}