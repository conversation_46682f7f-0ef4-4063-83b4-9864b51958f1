<template>
  <el-dialog
    title="题库文档导入"
    :visible.sync="dialogVisible"
    width="95%"
    :before-close="handleClose"
    append-to-body
    class="rich-text-import-dialog"
  >
    <div class="import-container">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" style="margin-bottom: 30px;">
        <el-step title="上传文档"></el-step>
        <el-step title="编辑内容"></el-step>
        <el-step title="预览确认"></el-step>
        <el-step title="导入完成"></el-step>
      </el-steps>

      <!-- 步骤1: 文档上传 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="upload-section">
          <h3>上传Word文档</h3>
          <p>请上传包含题目的Word文档(.docx格式)，系统将自动解析文档内容</p>

          <el-upload
            ref="fileUpload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleFileSuccess"
            :on-error="handleFileError"
            :before-upload="beforeFileUpload"
            :show-file-list="false"
            :accept=".docx"
            drag
          >
            <div class="upload-area">
              <i class="el-icon-upload"></i>
              <div class="upload-text">
                <p>将Word文档拖到此处，或<em>点击上传</em></p>
                <p class="upload-tip">支持 .docx 格式文件，文件大小不超过10MB</p>
              </div>
            </div>
          </el-upload>

          <div v-if="uploadedFile" class="uploaded-file">
            <el-alert
              :title="`已上传文件：${uploadedFile.name}`"
              type="success"
              :closable="false"
              show-icon
            />
          </div>

          <div v-if="uploading" class="uploading-status">
            <el-progress :percentage="uploadProgress" :show-text="false"></el-progress>
            <p>正在上传并解析文档...</p>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" :disabled="!uploadedFile" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤2: 富文本编辑 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="editor-container">
          <div class="editor-header">
            <h3>编辑文档内容</h3>
            <div class="editor-actions">
              <el-button size="small" @click="saveContent">保存内容</el-button>
              <el-button size="small" type="primary" @click="previewContent">实时预览</el-button>
            </div>
          </div>

          <div class="editor-preview-layout">
            <!-- 左侧富文本编辑器 -->
            <div class="editor-panel">
              <div class="panel-header">
                <h4>编辑区域</h4>
                <span class="panel-tip">您可以在此编辑题目内容</span>
              </div>
              <div class="editor-wrapper">
                <quill-editor
                  v-model="editContent"
                  :options="editorOptions"
                  @change="onContentChange"
                  class="content-editor"
                />
              </div>
            </div>

            <!-- 右侧预览区域 -->
            <div class="preview-panel">
              <div class="panel-header">
                <h4>实时预览</h4>
                <span class="panel-tip">题目解析结果：{{ previewQuestionCount }}道题目</span>
              </div>
              <div class="preview-wrapper">
                <div v-if="previewLoading" class="preview-loading">
                  <el-loading text="正在解析预览..."></el-loading>
                </div>
                <div v-else-if="previewHtml" class="preview-content" v-html="previewHtml"></div>
                <div v-else class="preview-empty">
                  <el-empty description="暂无预览内容，请编辑左侧内容"></el-empty>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" :disabled="!editContent" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤3: 预览确认 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="confirm-section">
          <h3>确认导入</h3>
          <div class="import-summary">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-statistic title="解析题目数量" :value="previewQuestionCount" suffix="道"></el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic title="解析错误" :value="parseErrors.length" suffix="个"></el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic title="目标题库" :value="bankName"></el-statistic>
              </el-col>
            </el-row>
          </div>

          <div v-if="parseErrors.length > 0" class="error-section">
            <h4>解析错误信息</h4>
            <el-alert
              v-for="(error, index) in parseErrors"
              :key="index"
              :title="error"
              type="warning"
              :closable="false"
              style="margin-bottom: 10px;"
            />
          </div>

          <div class="import-options">
            <h4>导入选项</h4>
            <el-checkbox v-model="importOptions.allowDuplicate">允许导入重复题目</el-checkbox>
          </div>

          <div class="final-preview">
            <h4>最终预览</h4>
            <div class="preview-content" v-html="previewHtml"></div>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" :loading="importing" @click="confirmImport">确认导入</el-button>
        </div>
      </div>

      <!-- 步骤4: 导入完成 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="result-section">
          <div class="result-icon">
            <i class="el-icon-success" style="font-size: 64px; color: #67c23a;"></i>
          </div>
          <h3>导入完成</h3>
          
          <div class="import-result">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-statistic title="成功导入" :value="importResult.successCount" suffix="道题目"></el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic title="导入失败" :value="importResult.failCount" suffix="道题目"></el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic title="总计处理" :value="importResult.successCount + importResult.failCount" suffix="道题目"></el-statistic>
              </el-col>
            </el-row>
          </div>

          <div v-if="importResult.errors && importResult.errors.length > 0" class="import-errors">
            <h4>导入错误详情</h4>
            <el-alert
              v-for="(error, index) in importResult.errors"
              :key="index"
              :title="error"
              type="error"
              :closable="false"
              style="margin-bottom: 10px;"
            />
          </div>
        </div>

        <div class="step-actions">
          <el-button type="primary" @click="handleComplete">完成</el-button>
          <el-button @click="resetImport">重新导入</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from '@/utils/auth'
import { uploadAndParse, saveEditedContent, previewContent, importFromEditor } from '@/api/biz/questionBank'
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'

export default {
  name: "RichTextImport",
  components: {
    quillEditor
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bankId: {
      type: [String, Number],
      required: true
    },
    bankName: {
      type: String,
      default: '题库'
    }
  },
  data() {
    return {
      dialogVisible: false,
      currentStep: 0,
      
      // 文件上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadAndParse',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      uploadedFile: null,
      uploading: false,
      uploadProgress: 0,
      
      // 编辑器相关
      editContent: '',
      editorOptions: {
        theme: 'snow',
        placeholder: '请编辑题目内容...',
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ 'header': 1 }, { 'header': 2 }],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'script': 'sub'}, { 'script': 'super' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            ['clean'],
            ['link', 'image']
          ]
        }
      },
      
      // 预览相关
      previewHtml: '',
      previewQuestionCount: 0,
      previewLoading: false,
      parseErrors: [],
      
      // 导入相关
      importing: false,
      importOptions: {
        allowDuplicate: false
      },
      importResult: {
        successCount: 0,
        failCount: 0,
        errors: []
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.resetImport()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 文件上传前验证
    beforeFileUpload(file) {
      const isDocx = file.name.toLowerCase().endsWith('.docx')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isDocx) {
        this.$message.error('只能上传.docx格式的Word文档!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10MB!')
        return false
      }

      this.uploading = true
      this.uploadProgress = 0
      return true
    },

    // 文件上传成功
    handleFileSuccess(response, file) {
      this.uploading = false
      if (response.code === 200) {
        this.uploadedFile = {
          name: file.name,
          response: response
        }
        this.editContent = response.data.editableContent || ''
        this.parseErrors = response.data.errors || []
        this.$message.success('文档上传成功')
      } else {
        this.$message.error(response.msg || '文档上传失败')
      }
    },

    // 文件上传失败
    handleFileError(err, file) {
      this.uploading = false
      console.error('文件上传失败', err)
      this.$message.error('文件上传失败')
    },

    // 编辑器内容变化
    onContentChange() {
      // 防抖处理，避免频繁请求
      clearTimeout(this.previewTimer)
      this.previewTimer = setTimeout(() => {
        this.autoPreview()
      }, 1000)
    },

    // 自动预览
    autoPreview() {
      if (!this.editContent || this.editContent.trim() === '') {
        this.previewHtml = ''
        this.previewQuestionCount = 0
        return
      }
      this.previewContent()
    },

    // 保存内容
    saveContent() {
      const data = {
        bankId: this.bankId,
        content: this.editContent
      }
      saveEditedContent(data).then(response => {
        if (response.code === 200) {
          this.$message.success('内容保存成功')
        } else {
          this.$message.error(response.msg || '保存失败')
        }
      }).catch(error => {
        console.error('保存内容失败', error)
        this.$message.error('保存失败')
      })
    },

    // 预览内容
    previewContent() {
      if (!this.editContent || this.editContent.trim() === '') {
        return
      }

      this.previewLoading = true
      const data = {
        content: this.editContent
      }
      
      previewContent(data).then(response => {
        this.previewLoading = false
        if (response.code === 200) {
          this.previewHtml = response.data.previewHtml || ''
          this.previewQuestionCount = response.data.questionCount || 0
          this.parseErrors = response.data.errors || []
        } else {
          this.$message.error(response.msg || '预览失败')
        }
      }).catch(error => {
        this.previewLoading = false
        console.error('预览失败', error)
        this.$message.error('预览失败')
      })
    },

    // 确认导入
    confirmImport() {
      this.importing = true
      const data = {
        bankId: this.bankId,
        content: this.editContent,
        allowDuplicate: this.importOptions.allowDuplicate
      }
      
      importFromEditor(data).then(response => {
        this.importing = false
        if (response.code === 200) {
          this.importResult = response.data
          this.nextStep()
          this.$message.success('题目导入成功')
        } else {
          this.$message.error(response.msg || '导入失败')
        }
      }).catch(error => {
        this.importing = false
        console.error('导入失败', error)
        this.$message.error('导入失败')
      })
    },

    // 下一步
    nextStep() {
      if (this.currentStep < 3) {
        this.currentStep++
        if (this.currentStep === 2) {
          // 进入预览确认步骤时，自动预览
          this.previewContent()
        }
      }
    },

    // 上一步
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },

    // 完成导入
    handleComplete() {
      this.$emit('success')
      this.handleClose()
    },

    // 重置导入
    resetImport() {
      this.currentStep = 0
      this.uploadedFile = null
      this.uploading = false
      this.uploadProgress = 0
      this.editContent = ''
      this.previewHtml = ''
      this.previewQuestionCount = 0
      this.parseErrors = []
      this.importing = false
      this.importOptions = {
        allowDuplicate: false
      }
      this.importResult = {
        successCount: 0,
        failCount: 0,
        errors: []
      }
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.rich-text-import-dialog {
  .el-dialog {
    margin-top: 5vh !important;
  }

  .el-dialog__body {
    padding: 20px;
  }
}

.import-container {
  min-height: 600px;
}

.step-content {
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.step-actions {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
  text-align: right;
}

/* 文件上传样式 */
.upload-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-section h3 {
  margin-bottom: 10px;
  color: #333;
}

.upload-section p {
  margin-bottom: 30px;
  color: #666;
  text-align: center;
}

.upload-area {
  text-align: center;
  padding: 40px;
}

.upload-area i {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 20px;
}

.upload-text p {
  margin: 10px 0;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

.uploaded-file {
  margin-top: 20px;
  width: 100%;
  max-width: 400px;
}

.uploading-status {
  margin-top: 20px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

/* 编辑器样式 */
.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.editor-header h3 {
  margin: 0;
  color: #333;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.editor-preview-layout {
  flex: 1;
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.editor-panel,
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.panel-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.panel-tip {
  font-size: 12px;
  color: #666;
}

.editor-wrapper,
.preview-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-editor {
  flex: 1;
  min-height: 350px;
}

.preview-wrapper {
  position: relative;
}

.preview-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
}

.preview-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  max-height: 350px;
}

.preview-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确认导入样式 */
.confirm-section {
  flex: 1;
}

.confirm-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.import-summary {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.error-section {
  margin-bottom: 30px;
}

.error-section h4 {
  margin-bottom: 15px;
  color: #f56c6c;
}

.import-options {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.import-options h4 {
  margin-bottom: 15px;
  color: #333;
}

.final-preview {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.final-preview h4 {
  margin: 0;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  color: #333;
}

.final-preview .preview-content {
  max-height: 300px;
  overflow-y: auto;
}

/* 完成页面样式 */
.result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.result-icon {
  margin-bottom: 20px;
}

.result-section h3 {
  margin-bottom: 30px;
  color: #333;
}

.import-result {
  margin-bottom: 30px;
  width: 100%;
  max-width: 600px;
}

.import-errors {
  width: 100%;
  max-width: 600px;
}

.import-errors h4 {
  margin-bottom: 15px;
  color: #f56c6c;
}

/* 预览内容样式 */
.preview-content .questions-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.preview-content .question-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: #fff;
}

.preview-content .question-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-weight: 500;
}

.preview-content .question-number {
  color: #409eff;
  font-weight: bold;
}

.preview-content .question-type {
  color: #409eff;
  font-weight: 500;
}

.preview-content .question-difficulty {
  color: #666;
  font-size: 12px;
}

.preview-content .question-content {
  margin-bottom: 15px;
  line-height: 1.6;
  color: #333;
}

.preview-content .question-options {
  margin-bottom: 15px;
}

.preview-content .option-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.preview-content .option-key {
  font-weight: 500;
  color: #409eff;
  min-width: 20px;
}

.preview-content .option-content {
  flex: 1;
  line-height: 1.5;
}

.preview-content .question-answer {
  margin-bottom: 10px;
  padding: 8px 12px;
  background: #e8f5e8;
  border-radius: 4px;
  font-size: 14px;
}

.preview-content .question-analysis {
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .editor-preview-layout {
    flex-direction: column;
  }

  .editor-panel,
  .preview-panel {
    min-height: 300px;
  }
}
</style>
